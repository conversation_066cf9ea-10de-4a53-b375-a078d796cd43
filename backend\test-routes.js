import { logger } from './src/utils/logger.js';

const testRoutes = async () => {
  const routes = [
    'health',
    'auth',
    'users',
    'customers',
    'serviceCalls',
    'masterData',
    'executives',
    'dashboard'
  ];

  for (const route of routes) {
    try {
      logger.info(`Testing ${route} routes...`);
      await import(`./src/routes/${route}.js`);
      logger.info(`✅ ${route} routes imported successfully`);
    } catch (error) {
      logger.error(`❌ ${route} routes import failed:`, error.message);
      console.error(`Full error for ${route}:`, error);
    }
  }
};

testRoutes();
