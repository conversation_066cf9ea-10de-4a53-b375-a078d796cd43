import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Container, Row, Col, Button } from 'react-bootstrap';
import { Helmet } from 'react-helmet-async';

const NotFound = () => {
  return (
    <>
      <Helmet>
        <title>Page Not Found - TallyCRM</title>
      </Helmet>
      
      <Container className="py-5">
        <Row className="justify-content-center text-center">
          <Col md={6}>
            <div className="mb-4">
              <i className="bi bi-exclamation-triangle text-warning" style={{ fontSize: '5rem' }}></i>
            </div>
            
            <h1 className="display-4 mb-3">404</h1>
            <h2 className="mb-3">Page Not Found</h2>
            <p className="text-muted mb-4">
              The page you are looking for might have been removed, had its name changed, 
              or is temporarily unavailable.
            </p>
            
            <div className="d-flex gap-2 justify-content-center">
              <Button as={Link} to="/" variant="primary">
                <i className="bi bi-house me-2"></i>
                Go Home
              </Button>
              <Button variant="outline-secondary" onClick={() => window.history.back()}>
                <i className="bi bi-arrow-left me-2"></i>
                Go Back
              </Button>
            </div>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default NotFound;
