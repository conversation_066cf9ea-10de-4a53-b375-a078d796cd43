import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { FaSave, FaTimes, FaUser, FaRupeeSign, FaCalendar, FaChartLine } from 'react-icons/fa';

const SalesForm = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEdit = Boolean(id);
  
  const [loading, setLoading] = useState(false);
  const [customers, setCustomers] = useState([]);
  const [salesTeam, setSalesTeam] = useState([]);
  const [products, setProducts] = useState([]);
  
  const [formData, setFormData] = useState({
    // Lead Information
    leadNumber: '',
    customerId: '',
    customerName: '',
    contactPerson: '',
    email: '',
    phone: '',
    
    // Product/Service
    productId: '',
    productType: '',
    description: '',
    
    // Sales Information
    stage: 'lead',
    status: 'active',
    priority: 'medium',
    source: '',
    assignedTo: '',
    
    // Financial
    expectedValue: '',
    probability: 50,
    currency: 'INR',
    
    // Timeline
    expectedCloseDate: '',
    
    // Additional
    requirements: '',
    notes: '',
    
    // Competition
    competitors: '',
    competitiveAdvantage: ''
  });

  const [errors, setErrors] = useState({});

  // Mock data for dropdowns
  useEffect(() => {
    const mockCustomers = [
      { id: 1, name: 'ABC Enterprises', contactPerson: 'John Doe', email: '<EMAIL>', phone: '+91 **********' },
      { id: 2, name: 'XYZ Trading Co.', contactPerson: 'Jane Smith', email: '<EMAIL>', phone: '+91 9876543211' },
      { id: 3, name: 'PQR Industries', contactPerson: 'Mike Johnson', email: '<EMAIL>', phone: '+91 **********' }
    ];
    
    const mockSalesTeam = [
      { id: 1, name: 'Rahul Sharma', specialization: 'Enterprise Sales' },
      { id: 2, name: 'Priya Patel', specialization: 'SME Sales' },
      { id: 3, name: 'Amit Singh', specialization: 'Channel Sales' },
      { id: 4, name: 'Vikash Gupta', specialization: 'Inside Sales' }
    ];
    
    const mockProducts = [
      { id: 1, name: 'Tally Prime', type: 'Software License', basePrice: 18000 },
      { id: 2, name: 'Tally ERP 9', type: 'Software License', basePrice: 15000 },
      { id: 3, name: 'Tally Silver', type: 'Software License', basePrice: 12000 },
      { id: 4, name: 'Tally Training', type: 'Service', basePrice: 8000 },
      { id: 5, name: 'Implementation Service', type: 'Service', basePrice: 25000 }
    ];
    
    setCustomers(mockCustomers);
    setSalesTeam(mockSalesTeam);
    setProducts(mockProducts);
    
    // Generate lead number for new leads
    if (!isEdit) {
      const leadNumber = `LEAD-${new Date().getFullYear()}-${String(Date.now()).slice(-3)}`;
      setFormData(prev => ({ ...prev, leadNumber }));
    }
  }, [isEdit]);

  // Mock data for edit mode
  useEffect(() => {
    if (isEdit) {
      setLoading(true);
      // Simulate API call
      setTimeout(() => {
        setFormData({
          leadNumber: 'LEAD-2024-001',
          customerId: '1',
          customerName: 'ABC Enterprises',
          contactPerson: 'John Doe',
          email: '<EMAIL>',
          phone: '+91 **********',
          productId: '1',
          productType: 'Software License',
          description: 'Tally Prime multi-user license for 10 users',
          stage: 'proposal',
          status: 'active',
          priority: 'high',
          source: 'Website',
          assignedTo: '1',
          expectedValue: '50000',
          probability: 75,
          currency: 'INR',
          expectedCloseDate: '2024-02-15',
          requirements: 'Multi-user setup with remote access capability',
          notes: 'Customer is very interested, decision expected by month end',
          competitors: 'Busy Accounting Software',
          competitiveAdvantage: 'Better integration with existing systems'
        });
        setLoading(false);
      }, 1000);
    }
  }, [isEdit]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
    
    // Auto-fill customer details when customer is selected
    if (name === 'customerId' && value) {
      const selectedCustomer = customers.find(c => c.id === parseInt(value));
      if (selectedCustomer) {
        setFormData(prev => ({
          ...prev,
          customerName: selectedCustomer.name,
          contactPerson: selectedCustomer.contactPerson,
          email: selectedCustomer.email,
          phone: selectedCustomer.phone
        }));
      }
    }
    
    // Auto-fill product details when product is selected
    if (name === 'productId' && value) {
      const selectedProduct = products.find(p => p.id === parseInt(value));
      if (selectedProduct) {
        setFormData(prev => ({
          ...prev,
          productType: selectedProduct.type,
          expectedValue: selectedProduct.basePrice.toString()
        }));
      }
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    // Required fields
    if (!formData.customerId) newErrors.customerId = 'Customer is required';
    if (!formData.contactPerson.trim()) newErrors.contactPerson = 'Contact person is required';
    if (!formData.email.trim()) newErrors.email = 'Email is required';
    if (!formData.phone.trim()) newErrors.phone = 'Phone is required';
    if (!formData.productId) newErrors.productId = 'Product/Service is required';
    if (!formData.assignedTo) newErrors.assignedTo = 'Sales person assignment is required';
    if (!formData.expectedValue.trim()) newErrors.expectedValue = 'Expected value is required';
    if (!formData.expectedCloseDate) newErrors.expectedCloseDate = 'Expected close date is required';
    
    // Email validation
    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email';
    }
    
    // Phone validation
    if (formData.phone && !/^\+?[\d\s-()]+$/.test(formData.phone)) {
      newErrors.phone = 'Please enter a valid phone number';
    }
    
    // Amount validation
    if (formData.expectedValue && (isNaN(formData.expectedValue) || parseFloat(formData.expectedValue) <= 0)) {
      newErrors.expectedValue = 'Please enter a valid amount';
    }
    
    // Probability validation
    if (formData.probability < 0 || formData.probability > 100) {
      newErrors.probability = 'Probability must be between 0 and 100';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast.error('Please fix the errors in the form');
      return;
    }
    
    setLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      toast.success(isEdit ? 'Sales opportunity updated successfully' : 'Sales opportunity created successfully');
      navigate('/sales');
    } catch (error) {
      toast.error('Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/sales');
  };

  if (loading && isEdit) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container-fluid">
      {/* Header */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h2 className="mb-0">{isEdit ? 'Edit Sales Opportunity' : 'New Sales Opportunity'}</h2>
              <p className="text-muted">
                {isEdit ? 'Update sales opportunity details' : 'Create a new sales lead or opportunity'}
              </p>
            </div>
            <div className="d-flex gap-2">
              <button 
                type="button" 
                className="btn btn-outline-secondary"
                onClick={handleCancel}
              >
                <FaTimes className="me-2" />
                Cancel
              </button>
              <button 
                type="submit" 
                form="salesForm"
                className="btn btn-primary"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                    {isEdit ? 'Updating...' : 'Creating...'}
                  </>
                ) : (
                  <>
                    <FaSave className="me-2" />
                    {isEdit ? 'Update Opportunity' : 'Create Opportunity'}
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Form */}
      <form id="salesForm" onSubmit={handleSubmit}>
        <div className="row">
          {/* Customer Information */}
          <div className="col-lg-6 mb-4">
            <div className="card h-100">
              <div className="card-header">
                <h5 className="card-title mb-0">
                  <FaUser className="me-2" />
                  Customer Information
                </h5>
              </div>
              <div className="card-body">
                <div className="row">
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Lead Number</label>
                    <input
                      type="text"
                      className="form-control"
                      name="leadNumber"
                      value={formData.leadNumber}
                      readOnly
                    />
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Customer *</label>
                    <select
                      className={`form-select ${errors.customerId ? 'is-invalid' : ''}`}
                      name="customerId"
                      value={formData.customerId}
                      onChange={handleInputChange}
                    >
                      <option value="">Select customer</option>
                      {customers.map(customer => (
                        <option key={customer.id} value={customer.id}>
                          {customer.name}
                        </option>
                      ))}
                    </select>
                    {errors.customerId && <div className="invalid-feedback">{errors.customerId}</div>}
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Contact Person *</label>
                    <input
                      type="text"
                      className={`form-control ${errors.contactPerson ? 'is-invalid' : ''}`}
                      name="contactPerson"
                      value={formData.contactPerson}
                      onChange={handleInputChange}
                      placeholder="Enter contact person name"
                    />
                    {errors.contactPerson && <div className="invalid-feedback">{errors.contactPerson}</div>}
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Email *</label>
                    <input
                      type="email"
                      className={`form-control ${errors.email ? 'is-invalid' : ''}`}
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      placeholder="Enter email address"
                    />
                    {errors.email && <div className="invalid-feedback">{errors.email}</div>}
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Phone *</label>
                    <input
                      type="tel"
                      className={`form-control ${errors.phone ? 'is-invalid' : ''}`}
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      placeholder="+91 **********"
                    />
                    {errors.phone && <div className="invalid-feedback">{errors.phone}</div>}
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Lead Source</label>
                    <select
                      className="form-select"
                      name="source"
                      value={formData.source}
                      onChange={handleInputChange}
                    >
                      <option value="">Select source</option>
                      <option value="Website">Website</option>
                      <option value="Referral">Referral</option>
                      <option value="Cold Call">Cold Call</option>
                      <option value="Trade Show">Trade Show</option>
                      <option value="Social Media">Social Media</option>
                      <option value="Advertisement">Advertisement</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Product & Sales Information */}
          <div className="col-lg-6 mb-4">
            <div className="card h-100">
              <div className="card-header">
                <h5 className="card-title mb-0">
                  <FaChartLine className="me-2" />
                  Product & Sales Information
                </h5>
              </div>
              <div className="card-body">
                <div className="row">
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Product/Service *</label>
                    <select
                      className={`form-select ${errors.productId ? 'is-invalid' : ''}`}
                      name="productId"
                      value={formData.productId}
                      onChange={handleInputChange}
                    >
                      <option value="">Select product/service</option>
                      {products.map(product => (
                        <option key={product.id} value={product.id}>
                          {product.name} ({product.type})
                        </option>
                      ))}
                    </select>
                    {errors.productId && <div className="invalid-feedback">{errors.productId}</div>}
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Product Type</label>
                    <input
                      type="text"
                      className="form-control"
                      name="productType"
                      value={formData.productType}
                      readOnly
                    />
                  </div>
                  
                  <div className="col-12 mb-3">
                    <label className="form-label">Description</label>
                    <textarea
                      className="form-control"
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      rows="3"
                      placeholder="Describe the product/service requirements"
                    ></textarea>
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Sales Stage</label>
                    <select
                      className="form-select"
                      name="stage"
                      value={formData.stage}
                      onChange={handleInputChange}
                    >
                      <option value="lead">Lead</option>
                      <option value="qualification">Qualification</option>
                      <option value="proposal">Proposal</option>
                      <option value="negotiation">Negotiation</option>
                      <option value="closed-won">Closed Won</option>
                      <option value="closed-lost">Closed Lost</option>
                    </select>
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Priority</label>
                    <select
                      className="form-select"
                      name="priority"
                      value={formData.priority}
                      onChange={handleInputChange}
                    >
                      <option value="low">Low</option>
                      <option value="medium">Medium</option>
                      <option value="high">High</option>
                      <option value="urgent">Urgent</option>
                    </select>
                  </div>
                  
                  <div className="col-12 mb-3">
                    <label className="form-label">Assigned To *</label>
                    <select
                      className={`form-select ${errors.assignedTo ? 'is-invalid' : ''}`}
                      name="assignedTo"
                      value={formData.assignedTo}
                      onChange={handleInputChange}
                    >
                      <option value="">Select sales person</option>
                      {salesTeam.map(person => (
                        <option key={person.id} value={person.id}>
                          {person.name} ({person.specialization})
                        </option>
                      ))}
                    </select>
                    {errors.assignedTo && <div className="invalid-feedback">{errors.assignedTo}</div>}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="row">
          {/* Financial Information */}
          <div className="col-lg-6 mb-4">
            <div className="card h-100">
              <div className="card-header">
                <h5 className="card-title mb-0">
                  <FaRupeeSign className="me-2" />
                  Financial Information
                </h5>
              </div>
              <div className="card-body">
                <div className="row">
                  <div className="col-md-8 mb-3">
                    <label className="form-label">Expected Value *</label>
                    <input
                      type="number"
                      className={`form-control ${errors.expectedValue ? 'is-invalid' : ''}`}
                      name="expectedValue"
                      value={formData.expectedValue}
                      onChange={handleInputChange}
                      placeholder="0"
                      min="0"
                      step="0.01"
                    />
                    {errors.expectedValue && <div className="invalid-feedback">{errors.expectedValue}</div>}
                  </div>
                  
                  <div className="col-md-4 mb-3">
                    <label className="form-label">Currency</label>
                    <select
                      className="form-select"
                      name="currency"
                      value={formData.currency}
                      onChange={handleInputChange}
                    >
                      <option value="INR">INR</option>
                      <option value="USD">USD</option>
                      <option value="EUR">EUR</option>
                    </select>
                  </div>
                  
                  <div className="col-12 mb-3">
                    <label className="form-label">Probability (%)</label>
                    <div className="d-flex align-items-center">
                      <input
                        type="range"
                        className="form-range me-3"
                        name="probability"
                        value={formData.probability}
                        onChange={handleInputChange}
                        min="0"
                        max="100"
                        step="5"
                      />
                      <span className="badge bg-primary">{formData.probability}%</span>
                    </div>
                  </div>
                  
                  <div className="col-12 mb-3">
                    <label className="form-label">Expected Close Date *</label>
                    <input
                      type="date"
                      className={`form-control ${errors.expectedCloseDate ? 'is-invalid' : ''}`}
                      name="expectedCloseDate"
                      value={formData.expectedCloseDate}
                      onChange={handleInputChange}
                      min={new Date().toISOString().split('T')[0]}
                    />
                    {errors.expectedCloseDate && <div className="invalid-feedback">{errors.expectedCloseDate}</div>}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Additional Information */}
          <div className="col-lg-6 mb-4">
            <div className="card h-100">
              <div className="card-header">
                <h5 className="card-title mb-0">Additional Information</h5>
              </div>
              <div className="card-body">
                <div className="row">
                  <div className="col-12 mb-3">
                    <label className="form-label">Requirements</label>
                    <textarea
                      className="form-control"
                      name="requirements"
                      value={formData.requirements}
                      onChange={handleInputChange}
                      rows="3"
                      placeholder="Customer requirements and specifications"
                    ></textarea>
                  </div>
                  
                  <div className="col-12 mb-3">
                    <label className="form-label">Notes</label>
                    <textarea
                      className="form-control"
                      name="notes"
                      value={formData.notes}
                      onChange={handleInputChange}
                      rows="3"
                      placeholder="Additional notes and comments"
                    ></textarea>
                  </div>
                  
                  <div className="col-12 mb-3">
                    <label className="form-label">Competitors</label>
                    <input
                      type="text"
                      className="form-control"
                      name="competitors"
                      value={formData.competitors}
                      onChange={handleInputChange}
                      placeholder="Competing vendors or solutions"
                    />
                  </div>
                  
                  <div className="col-12 mb-3">
                    <label className="form-label">Competitive Advantage</label>
                    <textarea
                      className="form-control"
                      name="competitiveAdvantage"
                      value={formData.competitiveAdvantage}
                      onChange={handleInputChange}
                      rows="2"
                      placeholder="Our advantages over competitors"
                    ></textarea>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
};

export default SalesForm;
