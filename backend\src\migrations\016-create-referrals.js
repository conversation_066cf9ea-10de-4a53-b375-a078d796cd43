import { DataTypes } from 'sequelize';

export const up = async (queryInterface) => {
  await queryInterface.createTable('referrals', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    customer_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'customers',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    referred_customer_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'customers',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    referral_code: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    referred_company_name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    referred_contact_person: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    referred_phone: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    referred_email: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    referred_address: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    referral_date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    status: {
      type: DataTypes.ENUM('pending', 'contacted', 'interested', 'converted', 'rejected', 'lost'),
      allowNull: false,
      defaultValue: 'pending',
    },
    first_contact_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    conversion_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    sale_amount: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    referral_commission_percentage: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    referral_commission_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    commission_paid: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    commission_paid_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    assigned_executive_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'executives',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    follow_up_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    rejection_reason: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    tags: {
      type: DataTypes.JSONB,
      defaultValue: [],
    },
    created_by: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'RESTRICT',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  });

  // Add indexes
  await queryInterface.addIndex('referrals', ['customer_id']);
  await queryInterface.addIndex('referrals', ['referred_customer_id']);
  await queryInterface.addIndex('referrals', ['referral_code'], { unique: true });
  await queryInterface.addIndex('referrals', ['status']);
  await queryInterface.addIndex('referrals', ['referral_date']);
  await queryInterface.addIndex('referrals', ['conversion_date']);
  await queryInterface.addIndex('referrals', ['commission_paid']);
  await queryInterface.addIndex('referrals', ['assigned_executive_id']);
  await queryInterface.addIndex('referrals', ['follow_up_date']);
  await queryInterface.addIndex('referrals', ['created_by']);
};

export const down = async (queryInterface) => {
  await queryInterface.dropTable('referrals');
};
