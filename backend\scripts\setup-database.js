import { Sequelize } from 'sequelize';
import bcrypt from 'bcryptjs';

// Configure database connection
const sequelize = new Sequelize({
  dialect: 'sqlite',
  storage: './database/tallycrm_dev.sqlite',
  logging: false,
});

async function setupDatabase() {
  try {
    console.log('Setting up database...');

    // Test connection
    await sequelize.authenticate();
    console.log('✓ Database connection established');

    // Create tables
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS tenants (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        subdomain TEXT UNIQUE NOT NULL,
        status TEXT DEFAULT 'active',
        plan TEXT DEFAULT 'basic',
        max_users INTEGER DEFAULT 10,
        settings TEXT DEFAULT '{}',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS permissions (
        id TEXT PRIMARY KEY,
        name TEXT UNIQUE NOT NULL,
        description TEXT,
        resource TEXT NOT NULL,
        action TEXT NOT NULL,
        scope TEXT DEFAULT 'tenant',
        is_system BOOLEAN DEFAULT false,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS roles (
        id TEXT PRIMARY KEY,
        tenant_id TEXT NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        is_system_role BOOLEAN DEFAULT false,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE
      )
    `);

    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS role_permissions (
        role_id TEXT NOT NULL,
        permission_id TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (role_id, permission_id),
        FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
        FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
      )
    `);

    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        tenant_id TEXT NOT NULL,
        email TEXT NOT NULL,
        password TEXT NOT NULL,
        first_name TEXT NOT NULL,
        last_name TEXT NOT NULL,
        phone TEXT,
        is_active BOOLEAN DEFAULT true,
        is_verified BOOLEAN DEFAULT false,
        last_login_at DATETIME,
        preferences TEXT DEFAULT '{}',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
        UNIQUE(tenant_id, email)
      )
    `);

    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS user_roles (
        user_id TEXT NOT NULL,
        role_id TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (user_id, role_id),
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
      )
    `);

    console.log('✓ Tables created');

    // Insert demo tenant
    const tenantId = '550e8400-e29b-41d4-a716-446655440100';
    await sequelize.query(`
      INSERT OR IGNORE INTO tenants (id, name, subdomain, status, plan, max_users, settings)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, {
      replacements: [tenantId, 'Demo Company', 'demo', 'active', 'pro', 50, JSON.stringify({
        timezone: 'Asia/Kolkata',
        currency: 'INR',
        date_format: 'DD/MM/YYYY',
        theme: 'light'
      })]
    });

    // Insert permissions
    const permissions = [
      ['550e8400-e29b-41d4-a716-446655440001', 'user.create', 'Create new users', 'users', 'create', 'tenant', true],
      ['550e8400-e29b-41d4-a716-446655440002', 'user.read', 'View user information', 'users', 'read', 'tenant', true],
      ['550e8400-e29b-41d4-a716-446655440003', 'user.update', 'Update user information', 'users', 'update', 'tenant', true],
      ['550e8400-e29b-41d4-a716-446655440004', 'user.delete', 'Delete users', 'users', 'delete', 'tenant', true],
      ['550e8400-e29b-41d4-a716-446655440020', 'customer.create', 'Create new customers', 'customers', 'create', 'tenant', true],
      ['550e8400-e29b-41d4-a716-446655440021', 'customer.read', 'View customer information', 'customers', 'read', 'tenant', true],
      ['550e8400-e29b-41d4-a716-446655440022', 'customer.update', 'Update customer information', 'customers', 'update', 'tenant', true],
      ['550e8400-e29b-41d4-a716-446655440030', 'service.create', 'Create service calls', 'services', 'create', 'tenant', true],
      ['550e8400-e29b-41d4-a716-446655440031', 'service.read', 'View service calls', 'services', 'read', 'tenant', true],
      ['550e8400-e29b-41d4-a716-446655440040', 'sale.create', 'Create sales records', 'sales', 'create', 'tenant', true],
      ['550e8400-e29b-41d4-a716-446655440041', 'sale.read', 'View sales records', 'sales', 'read', 'tenant', true],
      ['550e8400-e29b-41d4-a716-446655440050', 'report.view', 'View reports', 'reports', 'view', 'tenant', true],
      ['550e8400-e29b-41d4-a716-446655440060', 'system.admin', 'System administration access', 'system', 'admin', 'tenant', true],
    ];

    for (const perm of permissions) {
      await sequelize.query(`
        INSERT OR IGNORE INTO permissions (id, name, description, resource, action, scope, is_system)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `, {
        replacements: perm
      });
    }

    // Insert roles
    const roles = [
      ['550e8400-e29b-41d4-a716-446655440200', tenantId, 'Super Admin', 'Full system access', true],
      ['550e8400-e29b-41d4-a716-446655440201', tenantId, 'Admin', 'Administrative access', false],
      ['550e8400-e29b-41d4-a716-446655440202', tenantId, 'Manager', 'Management level access', false],
      ['550e8400-e29b-41d4-a716-446655440203', tenantId, 'User', 'Basic user access', false],
    ];

    for (const role of roles) {
      await sequelize.query(`
        INSERT OR IGNORE INTO roles (id, tenant_id, name, description, is_system_role)
        VALUES (?, ?, ?, ?, ?)
      `, {
        replacements: role
      });
    }

    // Assign permissions to Super Admin role
    const superAdminRoleId = '550e8400-e29b-41d4-a716-446655440200';
    for (const perm of permissions) {
      await sequelize.query(`
        INSERT OR IGNORE INTO role_permissions (role_id, permission_id)
        VALUES (?, ?)
      `, {
        replacements: [superAdminRoleId, perm[0]]
      });
    }

    // Create demo users
    const hashedPassword = await bcrypt.hash('Demo@123', 12);
    const users = [
      ['550e8400-e29b-41d4-a716-446655440300', tenantId, '<EMAIL>', hashedPassword, 'Super', 'Admin', '+91-9876543210', true, true],
      ['550e8400-e29b-41d4-a716-446655440301', tenantId, '<EMAIL>', hashedPassword, 'John', 'Manager', '+91-9876543211', true, true],
    ];

    for (const user of users) {
      await sequelize.query(`
        INSERT OR IGNORE INTO users (id, tenant_id, email, password, first_name, last_name, phone, is_active, is_verified, preferences)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, {
        replacements: [...user, JSON.stringify({ theme: 'light', language: 'en', timezone: 'Asia/Kolkata' })]
      });
    }

    // Assign roles to users
    await sequelize.query(`
      INSERT OR IGNORE INTO user_roles (user_id, role_id) VALUES (?, ?)
    `, {
      replacements: ['550e8400-e29b-41d4-a716-446655440300', superAdminRoleId]
    });

    await sequelize.query(`
      INSERT OR IGNORE INTO user_roles (user_id, role_id) VALUES (?, ?)
    `, {
      replacements: ['550e8400-e29b-41d4-a716-446655440301', '550e8400-e29b-41d4-a716-446655440202']
    });

    console.log('✓ Demo data inserted');
    console.log('');
    console.log('Demo users created:');
    console.log('  Email: <EMAIL>');
    console.log('  Password: Demo@123');
    console.log('  Role: Super Admin');
    console.log('');
    console.log('  Email: <EMAIL>');
    console.log('  Password: Demo@123');
    console.log('  Role: Manager');
    console.log('');
    console.log('✅ Database setup complete!');

  } catch (error) {
    console.error('❌ Database setup failed:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

setupDatabase();
