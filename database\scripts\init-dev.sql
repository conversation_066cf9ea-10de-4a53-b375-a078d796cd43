-- TallyCRM Development Database Initialization
-- This script sets up the development database with initial data

\c tallycrm_dev;

-- Set timezone
SET timezone = 'Asia/Kolkata';

-- Create audit trigger function
CREATE OR REPLACE FUNCTION audit.audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        INSERT INTO audit.audit_log (
            table_name,
            operation,
            old_values,
            changed_by,
            changed_at
        ) VALUES (
            TG_TABLE_NAME,
            TG_OP,
            row_to_json(OLD),
            current_user,
            NOW()
        );
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO audit.audit_log (
            table_name,
            operation,
            old_values,
            new_values,
            changed_by,
            changed_at
        ) VALUES (
            TG_TABLE_NAME,
            TG_OP,
            row_to_json(OLD),
            row_to_json(NEW),
            current_user,
            NOW()
        );
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO audit.audit_log (
            table_name,
            operation,
            new_values,
            changed_by,
            changed_at
        ) VALUES (
            TG_TABLE_NAME,
            TG_OP,
            row_to_json(NEW),
            current_user,
            NOW()
        );
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create audit log table
CREATE TABLE IF NOT EXISTS audit.audit_log (
    id SERIAL PRIMARY KEY,
    table_name TEXT NOT NULL,
    operation TEXT NOT NULL,
    old_values JSONB,
    new_values JSONB,
    changed_by TEXT NOT NULL,
    changed_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create indexes for audit log
CREATE INDEX IF NOT EXISTS idx_audit_log_table_name ON audit.audit_log(table_name);
CREATE INDEX IF NOT EXISTS idx_audit_log_changed_at ON audit.audit_log(changed_at);
CREATE INDEX IF NOT EXISTS idx_audit_log_changed_by ON audit.audit_log(changed_by);

-- Create application log table
CREATE TABLE IF NOT EXISTS logs.application_logs (
    id SERIAL PRIMARY KEY,
    level VARCHAR(20) NOT NULL,
    message TEXT NOT NULL,
    meta JSONB,
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    source VARCHAR(100),
    user_id INTEGER,
    session_id VARCHAR(255),
    ip_address INET,
    user_agent TEXT
);

-- Create indexes for application logs
CREATE INDEX IF NOT EXISTS idx_app_logs_level ON logs.application_logs(level);
CREATE INDEX IF NOT EXISTS idx_app_logs_timestamp ON logs.application_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_app_logs_user_id ON logs.application_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_app_logs_source ON logs.application_logs(source);

-- Create function to clean old logs
CREATE OR REPLACE FUNCTION logs.clean_old_logs(days_to_keep INTEGER DEFAULT 90)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM logs.application_logs 
    WHERE timestamp < NOW() - INTERVAL '1 day' * days_to_keep;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    DELETE FROM audit.audit_log 
    WHERE changed_at < NOW() - INTERVAL '1 day' * (days_to_keep * 2);
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create function to get database statistics
CREATE OR REPLACE FUNCTION public.get_db_stats()
RETURNS TABLE (
    table_name TEXT,
    row_count BIGINT,
    total_size TEXT,
    index_size TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        schemaname||'.'||tablename as table_name,
        n_tup_ins - n_tup_del as row_count,
        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as total_size,
        pg_size_pretty(pg_indexes_size(schemaname||'.'||tablename)) as index_size
    FROM pg_stat_user_tables 
    ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
END;
$$ LANGUAGE plpgsql;

-- Create function for full-text search
CREATE OR REPLACE FUNCTION public.create_search_vector(
    title TEXT DEFAULT '',
    content TEXT DEFAULT '',
    tags TEXT DEFAULT ''
)
RETURNS tsvector AS $$
BEGIN
    RETURN to_tsvector('english', 
        COALESCE(title, '') || ' ' || 
        COALESCE(content, '') || ' ' || 
        COALESCE(tags, '')
    );
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Grant permissions to application user
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO tallycrm_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA audit TO tallycrm_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA logs TO tallycrm_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO tallycrm_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA audit TO tallycrm_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA logs TO tallycrm_user;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO tallycrm_user;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA audit TO tallycrm_user;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA logs TO tallycrm_user;

-- Set default privileges for future objects
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO tallycrm_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA audit GRANT ALL ON TABLES TO tallycrm_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA logs GRANT ALL ON TABLES TO tallycrm_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO tallycrm_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA audit GRANT ALL ON SEQUENCES TO tallycrm_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA logs GRANT ALL ON SEQUENCES TO tallycrm_user;

-- Create development-specific settings
INSERT INTO logs.application_logs (level, message, source) 
VALUES ('INFO', 'Development database initialized successfully', 'database_setup');

-- Display setup completion message
SELECT 'TallyCRM Development Database Setup Complete!' as status;
