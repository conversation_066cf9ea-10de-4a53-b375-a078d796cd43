import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate, Link } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { 
  FaEdit, 
  FaTrash, 
  FaUser, 
  FaCalendar,
  FaClock,
  FaRupeeSign,
  FaTools,
  FaMapMarkerAlt,
  FaPhone,
  FaEnvelope,
  FaCheckCircle,
  FaTimesCircle,
  FaPlayCircle,
  FaPauseCircle
} from 'react-icons/fa';

const ServiceDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [service, setService] = useState(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('details');

  // Mock data - replace with API call
  useEffect(() => {
    const mockService = {
      id: 1,
      serviceNumber: 'SRV-2024-001',
      customer: 'ABC Enterprises',
      customerId: 1,
      contact<PERSON><PERSON>: '<PERSON>',
      contactPhone: '+91 9876543210',
      contactEmail: '<EMAIL>',
      type: 'Installation',
      category: 'Software',
      description: 'Tally Prime Installation and Setup with multi-user configuration',
      priority: 'high',
      status: 'in-progress',
      assignedTo: 'Raj Kumar',
      technicianId: 1,
      technicianPhone: '+91 9876543220',
      technicianEmail: '<EMAIL>',
      
      // Scheduling
      createdDate: '2024-01-15',
      scheduledDate: '2024-01-20',
      scheduledTime: '10:00',
      startedDate: '2024-01-20',
      completedDate: null,
      estimatedHours: 8,
      actualHours: 6,
      
      // Location
      serviceLocation: 'customer-site',
      address: '123 Business Park, Sector 5',
      city: 'Mumbai',
      state: 'Maharashtra',
      
      // Financial
      amount: 15000,
      currency: 'INR',
      paymentStatus: 'pending',
      
      // Additional
      requirements: 'Latest version of Tally Prime with multi-user license setup',
      notes: 'Customer prefers morning slot. Backup existing data before installation.',
      
      // Progress tracking
      progress: [
        {
          id: 1,
          timestamp: '2024-01-15 09:30',
          action: 'Service request created',
          user: 'System',
          description: 'Service request SRV-2024-001 created'
        },
        {
          id: 2,
          timestamp: '2024-01-15 10:15',
          action: 'Assigned to technician',
          user: 'Admin',
          description: 'Service assigned to Raj Kumar'
        },
        {
          id: 3,
          timestamp: '2024-01-20 10:00',
          action: 'Service started',
          user: 'Raj Kumar',
          description: 'Started Tally Prime installation process'
        },
        {
          id: 4,
          timestamp: '2024-01-20 14:30',
          action: 'Progress update',
          user: 'Raj Kumar',
          description: 'Software installation completed. Configuring multi-user setup.'
        }
      ],
      
      // Files/attachments
      attachments: [
        {
          id: 1,
          name: 'Installation_Checklist.pdf',
          size: '245 KB',
          uploadedBy: 'Raj Kumar',
          uploadedDate: '2024-01-20'
        },
        {
          id: 2,
          name: 'System_Requirements.docx',
          size: '128 KB',
          uploadedBy: 'Admin',
          uploadedDate: '2024-01-15'
        }
      ]
    };
    
    setTimeout(() => {
      setService(mockService);
      setLoading(false);
    }, 1000);
  }, [id]);

  const handleDelete = () => {
    if (window.confirm('Are you sure you want to delete this service request? This action cannot be undone.')) {
      toast.success('Service request deleted successfully');
      navigate('/services');
    }
  };

  const handleStatusUpdate = (newStatus) => {
    setService(prev => ({ ...prev, status: newStatus }));
    toast.success(`Service status updated to ${newStatus}`);
  };

  const getStatusBadge = (status) => {
    const badgeClass = status === 'completed' ? 'bg-success' : 
                      status === 'in-progress' ? 'bg-info' : 
                      status === 'scheduled' ? 'bg-warning' : 
                      status === 'pending' ? 'bg-secondary' : 'bg-danger';
    return <span className={`badge ${badgeClass}`}>{status.toUpperCase().replace('-', ' ')}</span>;
  };

  const getPriorityBadge = (priority) => {
    const badgeClass = priority === 'high' ? 'bg-danger' : 
                      priority === 'medium' ? 'bg-warning' : 'bg-success';
    return <span className={`badge ${badgeClass}`}>{priority.toUpperCase()}</span>;
  };

  const getPaymentStatusBadge = (status) => {
    const badgeClass = status === 'paid' ? 'bg-success' : 
                      status === 'partial' ? 'bg-info' :
                      status === 'overdue' ? 'bg-danger' : 'bg-warning';
    return <span className={`badge ${badgeClass}`}>{status.toUpperCase()}</span>;
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  if (!service) {
    return (
      <div className="container-fluid">
        <div className="alert alert-danger" role="alert">
          Service request not found.
        </div>
      </div>
    );
  }

  return (
    <div className="container-fluid">
      {/* Header */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="d-flex justify-content-between align-items-start">
            <div>
              <h2 className="mb-1">{service.serviceNumber}</h2>
              <p className="text-muted mb-2">{service.customer} • {service.type}</p>
              <div className="d-flex align-items-center gap-3">
                {getStatusBadge(service.status)}
                {getPriorityBadge(service.priority)}
                <span className="badge bg-info">{service.category}</span>
                <small className="text-muted">
                  <FaCalendar className="me-1" />
                  Created {new Date(service.createdDate).toLocaleDateString()}
                </small>
              </div>
            </div>
            <div className="d-flex gap-2">
              {/* Status Action Buttons */}
              {service.status === 'pending' && (
                <button 
                  className="btn btn-success btn-sm"
                  onClick={() => handleStatusUpdate('in-progress')}
                >
                  <FaPlayCircle className="me-1" />
                  Start Service
                </button>
              )}
              {service.status === 'in-progress' && (
                <>
                  <button 
                    className="btn btn-warning btn-sm"
                    onClick={() => handleStatusUpdate('scheduled')}
                  >
                    <FaPauseCircle className="me-1" />
                    Pause
                  </button>
                  <button 
                    className="btn btn-success btn-sm"
                    onClick={() => handleStatusUpdate('completed')}
                  >
                    <FaCheckCircle className="me-1" />
                    Complete
                  </button>
                </>
              )}
              
              <Link to={`/services/${service.id}/edit`} className="btn btn-outline-primary btn-sm">
                <FaEdit className="me-1" />
                Edit
              </Link>
              <button className="btn btn-outline-danger btn-sm" onClick={handleDelete}>
                <FaTrash className="me-1" />
                Delete
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="row mb-4">
        <div className="col-md-3">
          <div className="card bg-primary text-white">
            <div className="card-body">
              <div className="d-flex justify-content-between">
                <div>
                  <h4 className="mb-0">{service.actualHours}h</h4>
                  <p className="mb-0">Hours Spent</p>
                </div>
                <FaClock size={24} />
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card bg-info text-white">
            <div className="card-body">
              <div className="d-flex justify-content-between">
                <div>
                  <h4 className="mb-0">{service.estimatedHours}h</h4>
                  <p className="mb-0">Estimated</p>
                </div>
                <FaCalendar size={24} />
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card bg-success text-white">
            <div className="card-body">
              <div className="d-flex justify-content-between">
                <div>
                  <h4 className="mb-0">₹{service.amount.toLocaleString()}</h4>
                  <p className="mb-0">Service Amount</p>
                </div>
                <FaRupeeSign size={24} />
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card bg-warning text-white">
            <div className="card-body">
              <div className="d-flex justify-content-between">
                <div>
                  <h4 className="mb-0">{Math.round((service.actualHours / service.estimatedHours) * 100)}%</h4>
                  <p className="mb-0">Progress</p>
                </div>
                <FaTools size={24} />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="row">
        <div className="col-12">
          <ul className="nav nav-tabs mb-4">
            <li className="nav-item">
              <button 
                className={`nav-link ${activeTab === 'details' ? 'active' : ''}`}
                onClick={() => setActiveTab('details')}
              >
                Service Details
              </button>
            </li>
            <li className="nav-item">
              <button 
                className={`nav-link ${activeTab === 'progress' ? 'active' : ''}`}
                onClick={() => setActiveTab('progress')}
              >
                Progress ({service.progress.length})
              </button>
            </li>
            <li className="nav-item">
              <button 
                className={`nav-link ${activeTab === 'attachments' ? 'active' : ''}`}
                onClick={() => setActiveTab('attachments')}
              >
                Attachments ({service.attachments.length})
              </button>
            </li>
          </ul>

          {/* Tab Content */}
          {activeTab === 'details' && (
            <div className="row">
              {/* Service Information */}
              <div className="col-lg-6 mb-4">
                <div className="card h-100">
                  <div className="card-header">
                    <h5 className="card-title mb-0">
                      <FaTools className="me-2" />
                      Service Information
                    </h5>
                  </div>
                  <div className="card-body">
                    <div className="row">
                      <div className="col-sm-6 mb-3">
                        <strong>Service Type:</strong>
                        <p className="mb-0">{service.type}</p>
                      </div>
                      <div className="col-sm-6 mb-3">
                        <strong>Category:</strong>
                        <p className="mb-0">{service.category}</p>
                      </div>
                      <div className="col-12 mb-3">
                        <strong>Description:</strong>
                        <p className="mb-0">{service.description}</p>
                      </div>
                      <div className="col-sm-6 mb-3">
                        <strong>Scheduled Date:</strong>
                        <p className="mb-0">{new Date(service.scheduledDate).toLocaleDateString()}</p>
                      </div>
                      <div className="col-sm-6 mb-3">
                        <strong>Scheduled Time:</strong>
                        <p className="mb-0">{service.scheduledTime}</p>
                      </div>
                      <div className="col-12 mb-3">
                        <strong>Requirements:</strong>
                        <p className="mb-0">{service.requirements}</p>
                      </div>
                      <div className="col-12 mb-3">
                        <strong>Notes:</strong>
                        <p className="mb-0">{service.notes}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Customer & Contact Information */}
              <div className="col-lg-6 mb-4">
                <div className="card h-100">
                  <div className="card-header">
                    <h5 className="card-title mb-0">
                      <FaUser className="me-2" />
                      Customer & Contact
                    </h5>
                  </div>
                  <div className="card-body">
                    <div className="mb-3">
                      <strong>Customer:</strong>
                      <p className="mb-0">
                        <Link to={`/customers/${service.customerId}`} className="text-decoration-none">
                          {service.customer}
                        </Link>
                      </p>
                    </div>
                    
                    <div className="mb-3">
                      <strong>Contact Person:</strong>
                      <p className="mb-0">{service.contactPerson}</p>
                    </div>
                    
                    <div className="mb-3">
                      <div className="d-flex align-items-center mb-2">
                        <FaPhone className="text-muted me-2" />
                        <strong>Phone:</strong>
                      </div>
                      <p className="mb-0 ms-4">{service.contactPhone}</p>
                    </div>
                    
                    <div className="mb-3">
                      <div className="d-flex align-items-center mb-2">
                        <FaEnvelope className="text-muted me-2" />
                        <strong>Email:</strong>
                      </div>
                      <p className="mb-0 ms-4">{service.contactEmail}</p>
                    </div>
                    
                    {service.serviceLocation === 'customer-site' && (
                      <div className="mb-3">
                        <div className="d-flex align-items-center mb-2">
                          <FaMapMarkerAlt className="text-muted me-2" />
                          <strong>Service Address:</strong>
                        </div>
                        <p className="mb-0 ms-4">
                          {service.address}<br />
                          {service.city}, {service.state}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Technician Information */}
              <div className="col-lg-6 mb-4">
                <div className="card h-100">
                  <div className="card-header">
                    <h5 className="card-title mb-0">
                      <FaUser className="me-2" />
                      Assigned Technician
                    </h5>
                  </div>
                  <div className="card-body">
                    <div className="mb-3">
                      <strong>Technician:</strong>
                      <p className="mb-0">{service.assignedTo}</p>
                    </div>
                    
                    <div className="mb-3">
                      <div className="d-flex align-items-center mb-2">
                        <FaPhone className="text-muted me-2" />
                        <strong>Phone:</strong>
                      </div>
                      <p className="mb-0 ms-4">{service.technicianPhone}</p>
                    </div>
                    
                    <div className="mb-3">
                      <div className="d-flex align-items-center mb-2">
                        <FaEnvelope className="text-muted me-2" />
                        <strong>Email:</strong>
                      </div>
                      <p className="mb-0 ms-4">{service.technicianEmail}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Financial Information */}
              <div className="col-lg-6 mb-4">
                <div className="card h-100">
                  <div className="card-header">
                    <h5 className="card-title mb-0">
                      <FaRupeeSign className="me-2" />
                      Financial Information
                    </h5>
                  </div>
                  <div className="card-body">
                    <div className="row">
                      <div className="col-sm-6 mb-3">
                        <strong>Service Amount:</strong>
                        <p className="mb-0">₹{service.amount.toLocaleString()}</p>
                      </div>
                      <div className="col-sm-6 mb-3">
                        <strong>Currency:</strong>
                        <p className="mb-0">{service.currency}</p>
                      </div>
                      <div className="col-12 mb-3">
                        <strong>Payment Status:</strong>
                        <div className="mt-1">
                          {getPaymentStatusBadge(service.paymentStatus)}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'progress' && (
            <div className="row">
              <div className="col-12">
                <div className="card">
                  <div className="card-header">
                    <h5 className="card-title mb-0">Service Progress Timeline</h5>
                  </div>
                  <div className="card-body">
                    <div className="timeline">
                      {service.progress.map((item, index) => (
                        <div key={item.id} className="timeline-item">
                          <div className="timeline-marker bg-primary"></div>
                          <div className="timeline-content">
                            <div className="d-flex justify-content-between align-items-start">
                              <div>
                                <h6 className="mb-1">{item.action}</h6>
                                <p className="mb-1">{item.description}</p>
                                <small className="text-muted">by {item.user}</small>
                              </div>
                              <small className="text-muted">{new Date(item.timestamp).toLocaleString()}</small>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'attachments' && (
            <div className="row">
              <div className="col-12">
                <div className="card">
                  <div className="card-header d-flex justify-content-between align-items-center">
                    <h5 className="card-title mb-0">Service Attachments</h5>
                    <button className="btn btn-primary btn-sm">
                      Upload File
                    </button>
                  </div>
                  <div className="card-body">
                    <div className="table-responsive">
                      <table className="table table-hover">
                        <thead className="table-light">
                          <tr>
                            <th>File Name</th>
                            <th>Size</th>
                            <th>Uploaded By</th>
                            <th>Upload Date</th>
                            <th>Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          {service.attachments.map(file => (
                            <tr key={file.id}>
                              <td>{file.name}</td>
                              <td>{file.size}</td>
                              <td>{file.uploadedBy}</td>
                              <td>{new Date(file.uploadedDate).toLocaleDateString()}</td>
                              <td>
                                <div className="btn-group" role="group">
                                  <button className="btn btn-sm btn-outline-primary">
                                    Download
                                  </button>
                                  <button className="btn btn-sm btn-outline-danger">
                                    Delete
                                  </button>
                                </div>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ServiceDetails;
