import dotenv from 'dotenv';

// Load test environment variables
dotenv.config({ path: '.env.test' });

// Set test environment
process.env.NODE_ENV = 'test';
process.env.PORT = '5001';

// Mock external services
global.mockServices = {
  email: {
    send: jest.fn().mockResolvedValue({ messageId: 'mock-message-id' }),
  },
  sms: {
    send: jest.fn().mockResolvedValue({ sid: 'mock-sms-id' }),
  },
};

// Global test utilities
global.testUtils = {
  createMockUser: () => ({
    id: 1,
    name: 'Test User',
    email: '<EMAIL>',
    role: 'user',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }),
  
  createMockToken: () => 'mock-jwt-token-' + Date.now(),
  
  createMockRequest: (overrides = {}) => ({
    body: {},
    params: {},
    query: {},
    headers: {},
    user: null,
    ...overrides,
  }),
  
  createMockResponse: () => {
    const res = {};
    res.status = jest.fn().mockReturnValue(res);
    res.json = jest.fn().mockReturnValue(res);
    res.send = jest.fn().mockReturnValue(res);
    res.setHeader = jest.fn().mockReturnValue(res);
    res.cookie = jest.fn().mockReturnValue(res);
    res.clearCookie = jest.fn().mockReturnValue(res);
    return res;
  },
};

// Console override for cleaner test output
const originalConsole = global.console;
global.console = {
  ...originalConsole,
  log: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Cleanup after tests
afterAll(() => {
  global.console = originalConsole;
});
