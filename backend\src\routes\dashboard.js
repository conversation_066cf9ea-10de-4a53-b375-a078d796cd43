import express from 'express';
import { query } from 'express-validator';
import { validate } from '../middleware/validation.js';
import { authenticateToken, requirePermission, requireTenantAccess } from '../middleware/auth.js';
import {
  getDashboardOverview,
  getDashboardCharts,
} from '../controllers/dashboardController.js';

const router = express.Router();

// Apply authentication and tenant access to all routes
router.use(authenticateToken);
router.use(requireTenantAccess);

/**
 * @route   GET /api/dashboard/overview
 * @desc    Get dashboard overview statistics
 * @access  Private (requires dashboard.read permission)
 */
router.get('/overview', [
  requirePermission('dashboard.read'),
], getDashboardOverview);

/**
 * @route   GET /api/dashboard/charts
 * @desc    Get dashboard charts data
 * @access  Private (requires dashboard.read permission)
 */
router.get('/charts', [
  requirePermission('dashboard.read'),
  query('period')
    .optional()
    .isIn(['7d', '30d', '3m', '1y'])
    .withMessage('Period must be one of: 7d, 30d, 3m, 1y'),
  validate,
], getDashboardCharts);

export default router;
