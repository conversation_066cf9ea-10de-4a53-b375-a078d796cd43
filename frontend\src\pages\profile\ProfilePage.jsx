import React, { useState } from 'react';
import { toast } from 'react-hot-toast';
import {
  FaUser,
  FaEdit,
  FaSave,
  FaTimes,
  FaCamera,
  FaShieldAlt,
  FaBell,
  FaKey,
  FaEye,
  FaEyeSlash
} from 'react-icons/fa';

const ProfilePage = () => {
  const [activeTab, setActiveTab] = useState('profile');
  const [isEditing, setIsEditing] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const [profileData, setProfileData] = useState({
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+91 9876543210',
    designation: 'Senior Sales Manager',
    department: 'Sales',
    employeeId: 'EMP001',
    joinDate: '2023-01-15',
    reportingManager: '<PERSON>',
    location: 'Mumbai, Maharashtra',
    bio: 'Experienced sales professional with 8+ years in Tally software sales and customer relationship management.',
    profilePicture: '/assets/default-avatar.png'
  });

  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    weeklyReports: true,
    monthlyReports: true,
    serviceUpdates: true,
    salesAlerts: true,
    systemAlerts: false
  });

  const [securitySettings, setSecuritySettings] = useState({
    twoFactorAuth: false,
    loginAlerts: true,
    sessionTimeout: 30,
    passwordExpiry: 90
  });

  const handleProfileChange = (field, value) => {
    setProfileData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePasswordChange = (field, value) => {
    setPasswordData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleNotificationChange = (field, value) => {
    setNotificationSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSecurityChange = (field, value) => {
    setSecuritySettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSaveProfile = () => {
    setIsEditing(false);
    toast.success('Profile updated successfully');
  };

  const handleChangePassword = () => {
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toast.error('New passwords do not match');
      return;
    }
    if (passwordData.newPassword.length < 8) {
      toast.error('Password must be at least 8 characters long');
      return;
    }

    setPasswordData({
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    });
    toast.success('Password changed successfully');
  };

  const handleSaveNotifications = () => {
    toast.success('Notification preferences updated');
  };

  const handleSaveSecurity = () => {
    toast.success('Security settings updated');
  };

  const tabs = [
    { id: 'profile', name: 'Profile Information', icon: <FaUser /> },
    { id: 'password', name: 'Change Password', icon: <FaKey /> },
    { id: 'notifications', name: 'Notifications', icon: <FaBell /> },
    { id: 'security', name: 'Security', icon: <FaShieldAlt /> }
  ];

  const renderProfileTab = () => (
    <div className="row">
      <div className="col-lg-4 mb-4">
        <div className="card text-center">
          <div className="card-body">
            <div className="position-relative d-inline-block mb-3">
              <img
                src={profileData.profilePicture}
                alt="Profile"
                className="rounded-circle"
                style={{ width: '120px', height: '120px', objectFit: 'cover' }}
              />
              <button className="btn btn-primary btn-sm position-absolute bottom-0 end-0 rounded-circle">
                <FaCamera />
              </button>
            </div>
            <h5>{profileData.firstName} {profileData.lastName}</h5>
            <p className="text-muted">{profileData.designation}</p>
            <p className="text-muted">{profileData.department}</p>
          </div>
        </div>
      </div>

      <div className="col-lg-8">
        <div className="card">
          <div className="card-header d-flex justify-content-between align-items-center">
            <h5 className="card-title mb-0">Personal Information</h5>
            {isEditing ? (
              <div className="btn-group">
                <button className="btn btn-success btn-sm" onClick={handleSaveProfile}>
                  <FaSave className="me-1" />
                  Save
                </button>
                <button className="btn btn-secondary btn-sm" onClick={() => setIsEditing(false)}>
                  <FaTimes className="me-1" />
                  Cancel
                </button>
              </div>
            ) : (
              <button className="btn btn-primary btn-sm" onClick={() => setIsEditing(true)}>
                <FaEdit className="me-1" />
                Edit
              </button>
            )}
          </div>
          <div className="card-body">
            <div className="row">
              <div className="col-md-6 mb-3">
                <label className="form-label">First Name</label>
                {isEditing ? (
                  <input
                    type="text"
                    className="form-control"
                    value={profileData.firstName}
                    onChange={(e) => handleProfileChange('firstName', e.target.value)}
                  />
                ) : (
                  <p className="form-control-plaintext">{profileData.firstName}</p>
                )}
              </div>

              <div className="col-md-6 mb-3">
                <label className="form-label">Last Name</label>
                {isEditing ? (
                  <input
                    type="text"
                    className="form-control"
                    value={profileData.lastName}
                    onChange={(e) => handleProfileChange('lastName', e.target.value)}
                  />
                ) : (
                  <p className="form-control-plaintext">{profileData.lastName}</p>
                )}
              </div>

              <div className="col-md-6 mb-3">
                <label className="form-label">Email</label>
                {isEditing ? (
                  <input
                    type="email"
                    className="form-control"
                    value={profileData.email}
                    onChange={(e) => handleProfileChange('email', e.target.value)}
                  />
                ) : (
                  <p className="form-control-plaintext">{profileData.email}</p>
                )}
              </div>

              <div className="col-md-6 mb-3">
                <label className="form-label">Phone</label>
                {isEditing ? (
                  <input
                    type="tel"
                    className="form-control"
                    value={profileData.phone}
                    onChange={(e) => handleProfileChange('phone', e.target.value)}
                  />
                ) : (
                  <p className="form-control-plaintext">{profileData.phone}</p>
                )}
              </div>

              <div className="col-md-6 mb-3">
                <label className="form-label">Designation</label>
                <p className="form-control-plaintext">{profileData.designation}</p>
              </div>

              <div className="col-md-6 mb-3">
                <label className="form-label">Department</label>
                <p className="form-control-plaintext">{profileData.department}</p>
              </div>

              <div className="col-md-6 mb-3">
                <label className="form-label">Employee ID</label>
                <p className="form-control-plaintext">{profileData.employeeId}</p>
              </div>

              <div className="col-md-6 mb-3">
                <label className="form-label">Join Date</label>
                <p className="form-control-plaintext">{new Date(profileData.joinDate).toLocaleDateString()}</p>
              </div>

              <div className="col-md-6 mb-3">
                <label className="form-label">Reporting Manager</label>
                <p className="form-control-plaintext">{profileData.reportingManager}</p>
              </div>

              <div className="col-md-6 mb-3">
                <label className="form-label">Location</label>
                {isEditing ? (
                  <input
                    type="text"
                    className="form-control"
                    value={profileData.location}
                    onChange={(e) => handleProfileChange('location', e.target.value)}
                  />
                ) : (
                  <p className="form-control-plaintext">{profileData.location}</p>
                )}
              </div>

              <div className="col-12 mb-3">
                <label className="form-label">Bio</label>
                {isEditing ? (
                  <textarea
                    className="form-control"
                    rows="3"
                    value={profileData.bio}
                    onChange={(e) => handleProfileChange('bio', e.target.value)}
                  />
                ) : (
                  <p className="form-control-plaintext">{profileData.bio}</p>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderPasswordTab = () => (
    <div className="row justify-content-center">
      <div className="col-lg-6">
        <div className="card">
          <div className="card-header">
            <h5 className="card-title mb-0">Change Password</h5>
          </div>
          <div className="card-body">
            <div className="mb-3">
              <label className="form-label">Current Password</label>
              <div className="input-group">
                <input
                  type={showCurrentPassword ? "text" : "password"}
                  className="form-control"
                  value={passwordData.currentPassword}
                  onChange={(e) => handlePasswordChange('currentPassword', e.target.value)}
                />
                <button
                  className="btn btn-outline-secondary"
                  type="button"
                  onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                >
                  {showCurrentPassword ? <FaEyeSlash /> : <FaEye />}
                </button>
              </div>
            </div>

            <div className="mb-3">
              <label className="form-label">New Password</label>
              <div className="input-group">
                <input
                  type={showNewPassword ? "text" : "password"}
                  className="form-control"
                  value={passwordData.newPassword}
                  onChange={(e) => handlePasswordChange('newPassword', e.target.value)}
                />
                <button
                  className="btn btn-outline-secondary"
                  type="button"
                  onClick={() => setShowNewPassword(!showNewPassword)}
                >
                  {showNewPassword ? <FaEyeSlash /> : <FaEye />}
                </button>
              </div>
              <small className="text-muted">Password must be at least 8 characters long</small>
            </div>

            <div className="mb-3">
              <label className="form-label">Confirm New Password</label>
              <div className="input-group">
                <input
                  type={showConfirmPassword ? "text" : "password"}
                  className="form-control"
                  value={passwordData.confirmPassword}
                  onChange={(e) => handlePasswordChange('confirmPassword', e.target.value)}
                />
                <button
                  className="btn btn-outline-secondary"
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
                </button>
              </div>
            </div>

            <button className="btn btn-primary" onClick={handleChangePassword}>
              <FaKey className="me-2" />
              Change Password
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderNotificationsTab = () => (
    <div className="row justify-content-center">
      <div className="col-lg-8">
        <div className="card">
          <div className="card-header d-flex justify-content-between align-items-center">
            <h5 className="card-title mb-0">Notification Preferences</h5>
            <button className="btn btn-primary btn-sm" onClick={handleSaveNotifications}>
              <FaSave className="me-1" />
              Save
            </button>
          </div>
          <div className="card-body">
            <div className="row">
              <div className="col-md-6 mb-3">
                <div className="form-check form-switch">
                  <input
                    className="form-check-input"
                    type="checkbox"
                    checked={notificationSettings.emailNotifications}
                    onChange={(e) => handleNotificationChange('emailNotifications', e.target.checked)}
                  />
                  <label className="form-check-label">Email Notifications</label>
                </div>
              </div>

              <div className="col-md-6 mb-3">
                <div className="form-check form-switch">
                  <input
                    className="form-check-input"
                    type="checkbox"
                    checked={notificationSettings.smsNotifications}
                    onChange={(e) => handleNotificationChange('smsNotifications', e.target.checked)}
                  />
                  <label className="form-check-label">SMS Notifications</label>
                </div>
              </div>

              <div className="col-md-6 mb-3">
                <div className="form-check form-switch">
                  <input
                    className="form-check-input"
                    type="checkbox"
                    checked={notificationSettings.pushNotifications}
                    onChange={(e) => handleNotificationChange('pushNotifications', e.target.checked)}
                  />
                  <label className="form-check-label">Push Notifications</label>
                </div>
              </div>

              <div className="col-md-6 mb-3">
                <div className="form-check form-switch">
                  <input
                    className="form-check-input"
                    type="checkbox"
                    checked={notificationSettings.weeklyReports}
                    onChange={(e) => handleNotificationChange('weeklyReports', e.target.checked)}
                  />
                  <label className="form-check-label">Weekly Reports</label>
                </div>
              </div>

              <div className="col-md-6 mb-3">
                <div className="form-check form-switch">
                  <input
                    className="form-check-input"
                    type="checkbox"
                    checked={notificationSettings.monthlyReports}
                    onChange={(e) => handleNotificationChange('monthlyReports', e.target.checked)}
                  />
                  <label className="form-check-label">Monthly Reports</label>
                </div>
              </div>

              <div className="col-md-6 mb-3">
                <div className="form-check form-switch">
                  <input
                    className="form-check-input"
                    type="checkbox"
                    checked={notificationSettings.serviceUpdates}
                    onChange={(e) => handleNotificationChange('serviceUpdates', e.target.checked)}
                  />
                  <label className="form-check-label">Service Updates</label>
                </div>
              </div>

              <div className="col-md-6 mb-3">
                <div className="form-check form-switch">
                  <input
                    className="form-check-input"
                    type="checkbox"
                    checked={notificationSettings.salesAlerts}
                    onChange={(e) => handleNotificationChange('salesAlerts', e.target.checked)}
                  />
                  <label className="form-check-label">Sales Alerts</label>
                </div>
              </div>

              <div className="col-md-6 mb-3">
                <div className="form-check form-switch">
                  <input
                    className="form-check-input"
                    type="checkbox"
                    checked={notificationSettings.systemAlerts}
                    onChange={(e) => handleNotificationChange('systemAlerts', e.target.checked)}
                  />
                  <label className="form-check-label">System Alerts</label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderSecurityTab = () => (
    <div className="row justify-content-center">
      <div className="col-lg-8">
        <div className="card">
          <div className="card-header d-flex justify-content-between align-items-center">
            <h5 className="card-title mb-0">Security Settings</h5>
            <button className="btn btn-primary btn-sm" onClick={handleSaveSecurity}>
              <FaSave className="me-1" />
              Save
            </button>
          </div>
          <div className="card-body">
            <div className="row">
              <div className="col-md-6 mb-3">
                <div className="form-check form-switch">
                  <input
                    className="form-check-input"
                    type="checkbox"
                    checked={securitySettings.twoFactorAuth}
                    onChange={(e) => handleSecurityChange('twoFactorAuth', e.target.checked)}
                  />
                  <label className="form-check-label">Two-Factor Authentication</label>
                </div>
                <small className="text-muted">Add an extra layer of security to your account</small>
              </div>

              <div className="col-md-6 mb-3">
                <div className="form-check form-switch">
                  <input
                    className="form-check-input"
                    type="checkbox"
                    checked={securitySettings.loginAlerts}
                    onChange={(e) => handleSecurityChange('loginAlerts', e.target.checked)}
                  />
                  <label className="form-check-label">Login Alerts</label>
                </div>
                <small className="text-muted">Get notified of new login attempts</small>
              </div>

              <div className="col-md-6 mb-3">
                <label className="form-label">Session Timeout (minutes)</label>
                <input
                  type="number"
                  className="form-control"
                  value={securitySettings.sessionTimeout}
                  onChange={(e) => handleSecurityChange('sessionTimeout', parseInt(e.target.value))}
                  min="5"
                  max="120"
                />
              </div>

              <div className="col-md-6 mb-3">
                <label className="form-label">Password Expiry (days)</label>
                <input
                  type="number"
                  className="form-control"
                  value={securitySettings.passwordExpiry}
                  onChange={(e) => handleSecurityChange('passwordExpiry', parseInt(e.target.value))}
                  min="30"
                  max="365"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'profile':
        return renderProfileTab();
      case 'password':
        return renderPasswordTab();
      case 'notifications':
        return renderNotificationsTab();
      case 'security':
        return renderSecurityTab();
      default:
        return renderProfileTab();
    }
  };

  return (
    <div className="container-fluid">
      {/* Header */}
      <div className="row mb-4">
        <div className="col-12">
          <h2 className="mb-0">My Profile</h2>
          <p className="text-muted">Manage your profile information and account settings</p>
        </div>
      </div>

      {/* Tabs */}
      <div className="row">
        <div className="col-12">
          <ul className="nav nav-tabs mb-4">
            {tabs.map(tab => (
              <li key={tab.id} className="nav-item">
                <button
                  className={`nav-link ${activeTab === tab.id ? 'active' : ''}`}
                  onClick={() => setActiveTab(tab.id)}
                >
                  {tab.icon}
                  <span className="ms-2">{tab.name}</span>
                </button>
              </li>
            ))}
          </ul>

          {/* Tab Content */}
          {renderTabContent()}
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
