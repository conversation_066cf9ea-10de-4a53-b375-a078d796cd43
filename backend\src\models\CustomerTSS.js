import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const CustomerTSS = sequelize.define('CustomerTSS', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    customer_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'customers',
        key: 'id',
      },
    },
    license_edition_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'license_editions',
        key: 'id',
      },
    },
    tss_id: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      comment: 'Tally Software Service ID',
    },
    license_key: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Tally license key',
    },
    installation_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    activation_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    expiry_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    renewal_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    status: {
      type: DataTypes.ENUM('active', 'expired', 'suspended', 'cancelled'),
      allowNull: false,
      defaultValue: 'active',
    },
    version: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Tally software version',
    },
    installation_path: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Software installation path',
    },
    data_path: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Tally data directory path',
    },
    companies_count: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0,
      comment: 'Number of companies created',
    },
    users_count: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 1,
      comment: 'Number of users configured',
    },
    last_backup_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    backup_location: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Backup storage location',
    },
    auto_backup_enabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    remote_access_enabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    tally_net_enabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    gst_enabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    payroll_enabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    pos_enabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    system_requirements: {
      type: DataTypes.JSONB,
      defaultValue: {},
      comment: 'System requirements and specifications',
    },
    installation_notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    configuration_details: {
      type: DataTypes.JSONB,
      defaultValue: {},
      comment: 'Configuration settings and customizations',
    },
    addons: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Array of installed addons and plugins',
    },
    customizations: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Array of customizations made',
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'customer_tss',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['customer_id'],
      },
      {
        fields: ['tss_id'],
        unique: true,
      },
      {
        fields: ['license_edition_id'],
      },
      {
        fields: ['status'],
      },
      {
        fields: ['expiry_date'],
      },
      {
        fields: ['renewal_date'],
      },
      {
        fields: ['is_active'],
      },
    ],
  });

  // Instance methods
  CustomerTSS.prototype.isExpired = function() {
    if (!this.expiry_date) return false;
    return new Date(this.expiry_date) < new Date();
  };

  CustomerTSS.prototype.isExpiringSoon = function(days = 30) {
    if (!this.expiry_date) return false;
    const expiryDate = new Date(this.expiry_date);
    const warningDate = new Date();
    warningDate.setDate(warningDate.getDate() + days);
    return expiryDate <= warningDate && expiryDate >= new Date();
  };

  CustomerTSS.prototype.getDaysUntilExpiry = function() {
    if (!this.expiry_date) return null;
    const expiryDate = new Date(this.expiry_date);
    const today = new Date();
    const diffTime = expiryDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  CustomerTSS.prototype.getDaysUntilRenewal = function() {
    if (!this.renewal_date) return null;
    const renewalDate = new Date(this.renewal_date);
    const today = new Date();
    const diffTime = renewalDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  CustomerTSS.prototype.getUsageAge = function() {
    if (!this.installation_date) return null;
    const installDate = new Date(this.installation_date);
    const today = new Date();
    const diffTime = today - installDate;
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    const years = Math.floor(diffDays / 365);
    const months = Math.floor((diffDays % 365) / 30);
    return { years, months, totalDays: diffDays };
  };

  CustomerTSS.prototype.needsBackup = function(days = 7) {
    if (!this.last_backup_date) return true;
    const lastBackup = new Date(this.last_backup_date);
    const warningDate = new Date();
    warningDate.setDate(warningDate.getDate() - days);
    return lastBackup < warningDate;
  };

  CustomerTSS.prototype.getStatusColor = function() {
    const colors = {
      active: '#28a745',
      expired: '#dc3545',
      suspended: '#ffc107',
      cancelled: '#6c757d',
    };
    return colors[this.status] || '#6c757d';
  };

  // Associations
  CustomerTSS.associate = function(models) {
    CustomerTSS.belongsTo(models.Customer, {
      foreignKey: 'customer_id',
      as: 'customer',
    });

    CustomerTSS.belongsTo(models.LicenseEdition, {
      foreignKey: 'license_edition_id',
      as: 'licenseEdition',
    });

    CustomerTSS.hasMany(models.ServiceCall, {
      foreignKey: 'tss_id',
      as: 'serviceCalls',
    });

    CustomerTSS.hasMany(models.CustomerAMC, {
      foreignKey: 'tss_id',
      as: 'amcContracts',
    });
  };

  return CustomerTSS;
}
