# 🔧 SaaS CRM for Tally Resellers - Development Environment Setup

## 📋 Quick Start Guide

### Windows Setup
```powershell
# Install Node.js (Download from nodejs.org)
# Install PostgreSQL (Download from postgresql.org)
# Install Git (Download from git-scm.com)

# Verify installations
node --version
npm --version
psql --version
git --version

# Install global packages
npm install -g nodemon concurrently
```

### macOS Setup
```bash
# Install Homebrew (if not installed)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install required tools
brew install node postgresql git

# Start PostgreSQL service
brew services start postgresql

# Install global packages
npm install -g nodemon concurrently
```

### Linux (Ubuntu/Debian) Setup
```bash
# Update package list
sudo apt update

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PostgreSQL
sudo apt install postgresql postgresql-contrib

# Install Git
sudo apt install git

# Start PostgreSQL service
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Install global packages
npm install -g nodemon concurrently
```

## 🗄️ Database Setup

### PostgreSQL Configuration

#### 1. Create Database User
```sql
-- Connect to PostgreSQL as superuser
sudo -u postgres psql

-- Create development user
CREATE USER tallycrm_dev WITH PASSWORD 'your_password';
ALTER USER tallycrm_dev CREATEDB;

-- Create databases
CREATE DATABASE tallycrm_dev OWNER tallycrm_dev;
CREATE DATABASE tallycrm_test OWNER tallycrm_dev;

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE tallycrm_dev TO tallycrm_dev;
GRANT ALL PRIVILEGES ON DATABASE tallycrm_test TO tallycrm_dev;

-- Exit PostgreSQL
\q
```

#### 2. Configure Connection
Update `backend/.env` file:
```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=tallycrm_dev
DB_USER=tallycrm_dev
DB_PASSWORD=your_password
DB_SSL=false

# Test Database
TEST_DB_NAME=tallycrm_test
```

#### 3. Test Database Connection
```bash
cd backend
node -e "
const { Sequelize } = require('sequelize');
const sequelize = new Sequelize(process.env.DB_NAME, process.env.DB_USER, process.env.DB_PASSWORD, {
  host: process.env.DB_HOST,
  dialect: 'postgres'
});
sequelize.authenticate().then(() => console.log('Database connected!')).catch(err => console.error('Database connection failed:', err));
"
```

## 🚀 Project Setup

### 1. Environment Configuration

#### Backend Environment (.env)
```env
# Server Configuration
NODE_ENV=development
PORT=3000
API_BASE_URL=http://localhost:3000

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=tallycrm_dev
DB_USER=tallycrm_dev
DB_PASSWORD=your_password
DB_SSL=false

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your_refresh_secret_here
JWT_REFRESH_EXPIRES_IN=7d

# Email Configuration (Optional for development)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# Google Maps API (Optional)
GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=debug
LOG_FILE=./logs/app.log
```

#### Frontend Environment (.env)
```env
# API Configuration
VITE_API_BASE_URL=http://localhost:3000/api
VITE_APP_NAME=Tally CRM SaaS
VITE_APP_VERSION=1.0.0

# Google Maps Configuration
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# Development Configuration
VITE_NODE_ENV=development
VITE_DEBUG=true

# Feature Flags
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_NOTIFICATIONS=true
```

### 2. Install Dependencies
```bash
# Install all dependencies
npm run install:all

# Or install individually
cd backend && npm install
cd ../frontend && npm install
```

### 3. Database Migration and Seeding
```bash
cd backend

# Run database migrations
npm run migrate

# Seed initial data
npm run seed

# Verify database setup
npm run db:status
```

## 🔧 Development Tools Setup

### VS Code Extensions
Install these recommended extensions:
```json
{
  "recommendations": [
    "ms-vscode.vscode-eslint",
    "esbenp.prettier-vscode",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-json",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "ms-vscode.vscode-typescript-next",
    "ms-vscode-remote.remote-containers",
    "ms-vscode.vscode-postgres"
  ]
}
```

### VS Code Settings
Create `.vscode/settings.json`:
```json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "emmet.includeLanguages": {
    "javascript": "javascriptreact"
  },
  "files.associations": {
    "*.jsx": "javascriptreact"
  },
  "typescript.preferences.importModuleSpecifier": "relative"
}
```

### ESLint Configuration
Backend `.eslintrc.js`:
```javascript
module.exports = {
  env: {
    node: true,
    es2021: true,
    jest: true
  },
  extends: ['eslint:recommended'],
  parserOptions: {
    ecmaVersion: 12,
    sourceType: 'module'
  },
  rules: {
    'no-console': 'warn',
    'no-unused-vars': 'error',
    'prefer-const': 'error'
  }
};
```

Frontend `.eslintrc.js`:
```javascript
module.exports = {
  env: {
    browser: true,
    es2021: true
  },
  extends: [
    'eslint:recommended',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended'
  ],
  parserOptions: {
    ecmaFeatures: {
      jsx: true
    },
    ecmaVersion: 12,
    sourceType: 'module'
  },
  plugins: ['react'],
  rules: {
    'react/prop-types': 'off',
    'react/react-in-jsx-scope': 'off'
  },
  settings: {
    react: {
      version: 'detect'
    }
  }
};
```

## 🏃‍♂️ Running the Application

### Development Mode
```bash
# Start both frontend and backend
npm run dev

# Or start individually
npm run dev:backend    # Backend on http://localhost:3000
npm run dev:frontend   # Frontend on http://localhost:5173
```

### Production Build
```bash
# Build frontend for production
cd frontend
npm run build

# Start backend in production mode
cd backend
NODE_ENV=production npm start
```

### Docker Development (Optional)
```bash
# Start with Docker Compose
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

## 🧪 Testing Setup

### Backend Testing
```bash
cd backend

# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### Frontend Testing
```bash
cd frontend

# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### Integration Testing
```bash
# Run full test suite
npm run test:all

# Run E2E tests (if configured)
npm run test:e2e
```

## 🔍 Debugging Setup

### Backend Debugging (VS Code)
Create `.vscode/launch.json`:
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Backend",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/backend/server.js",
      "env": {
        "NODE_ENV": "development"
      },
      "console": "integratedTerminal",
      "restart": true,
      "runtimeExecutable": "nodemon",
      "skipFiles": ["<node_internals>/**"]
    }
  ]
}
```

### Frontend Debugging
- Use React Developer Tools browser extension
- Use browser DevTools for debugging
- VS Code debugger for Chrome extension

### Database Debugging
```bash
# Connect to development database
psql -h localhost -U tallycrm_dev -d tallycrm_dev

# View all tables
\dt

# Describe table structure
\d table_name

# View table data
SELECT * FROM table_name LIMIT 10;
```

## 🚨 Troubleshooting

### Common Issues and Solutions

#### 1. Database Connection Issues
```bash
# Check PostgreSQL service status
sudo systemctl status postgresql

# Restart PostgreSQL
sudo systemctl restart postgresql

# Check database exists
psql -h localhost -U tallycrm_dev -l
```

#### 2. Port Already in Use
```bash
# Find process using port 3000
lsof -i :3000

# Kill process
kill -9 <PID>

# Or use different port
PORT=3001 npm run dev
```

#### 3. Node Modules Issues
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

#### 4. Environment Variables Not Loading
```bash
# Verify .env file exists and has correct format
cat .env

# Check if dotenv is installed
npm list dotenv

# Restart development server
npm run dev
```

#### 5. CORS Issues
Update backend CORS configuration:
```javascript
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:3000'],
  credentials: true
}));
```

### Performance Optimization
```bash
# Monitor memory usage
node --inspect server.js

# Profile application
npm install -g clinic
clinic doctor -- node server.js
```

### Logging and Monitoring
```bash
# View application logs
tail -f logs/app.log

# Monitor database queries
tail -f /var/log/postgresql/postgresql-14-main.log
```

## 📚 Additional Resources

### Documentation Links
- [Node.js Documentation](https://nodejs.org/docs/)
- [React Documentation](https://react.dev/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Express.js Documentation](https://expressjs.com/)
- [Vite Documentation](https://vitejs.dev/)

### Development Tools
- **API Testing**: Postman, Insomnia
- **Database Management**: pgAdmin, DBeaver
- **Version Control**: Git, GitHub Desktop
- **Code Quality**: ESLint, Prettier, SonarQube

**Next Steps**: After completing the development setup, proceed to [Database Design](DATABASE_DESIGN.md) for detailed database schema information.
