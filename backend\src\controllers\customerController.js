import models from '../models/index.js';
import { logger } from '../utils/logger.js';
import { Op } from 'sequelize';

/**
 * Get all customers with pagination and filters
 */
export const getCustomers = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search,
      customerType,
      industryId,
      areaId,
      assignedExecutiveId,
      isActive,
      sortBy = 'created_at',
      sortOrder = 'DESC',
    } = req.query;

    const offset = (page - 1) * limit;
    const where = { tenant_id: req.user.tenant.id };

    // Apply filters
    if (search) {
      where[Op.or] = [
        { company_name: { [Op.iLike]: `%${search}%` } },
        { customer_code: { [Op.iLike]: `%${search}%` } },
        { email: { [Op.iLike]: `%${search}%` } },
        { phone: { [Op.iLike]: `%${search}%` } },
      ];
    }

    if (customerType) {
      where.customer_type = customerType;
    }

    if (industryId) {
      where.industry_id = industryId;
    }

    if (areaId) {
      where.area_id = areaId;
    }

    if (assignedExecutiveId) {
      where.assigned_executive_id = assignedExecutiveId;
    }

    if (isActive !== undefined) {
      where.is_active = isActive === 'true';
    }

    const { count, rows: customers } = await models.Customer.findAndCountAll({
      where,
      include: [
        {
          model: models.Industry,
          as: 'industry',
          attributes: ['id', 'name', 'code'],
        },
        {
          model: models.Area,
          as: 'area',
          attributes: ['id', 'name', 'code', 'city', 'state'],
        },
        {
          model: models.Executive,
          as: 'assignedExecutive',
          attributes: ['id', 'first_name', 'last_name', 'email', 'phone'],
        },
        {
          model: models.User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email'],
        },
      ],
      limit: parseInt(limit),
      offset,
      order: [[sortBy, sortOrder.toUpperCase()]],
    });

    res.json({
      success: true,
      data: {
        customers,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: parseInt(limit),
        },
      },
    });

  } catch (error) {
    logger.error('Get customers error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch customers',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get customer by ID
 */
export const getCustomerById = async (req, res) => {
  try {
    const { id } = req.params;

    const customer = await models.Customer.findOne({
      where: {
        id,
        tenant_id: req.user.tenant.id,
      },
      include: [
        {
          model: models.Industry,
          as: 'industry',
        },
        {
          model: models.Area,
          as: 'area',
        },
        {
          model: models.Executive,
          as: 'assignedExecutive',
          include: [
            {
              model: models.Designation,
              as: 'designation',
            },
          ],
        },
        {
          model: models.CustomerContact,
          as: 'contacts',
          include: [
            {
              model: models.Designation,
              as: 'designation',
            },
          ],
        },
        {
          model: models.CustomerTSS,
          as: 'tssDetails',
          include: [
            {
              model: models.LicenseEdition,
              as: 'licenseEdition',
            },
          ],
        },
        {
          model: models.CustomerAMC,
          as: 'amcContracts',
        },
        {
          model: models.User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email'],
        },
      ],
    });

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found',
      });
    }

    res.json({
      success: true,
      data: { customer },
    });

  } catch (error) {
    logger.error('Get customer by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch customer',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Create new customer
 */
export const createCustomer = async (req, res) => {
  try {
    const customerData = {
      ...req.body,
      tenant_id: req.user.tenant.id,
      created_by: req.user.id,
    };

    // Generate customer code if not provided
    if (!customerData.customer_code) {
      const lastCustomer = await models.Customer.findOne({
        where: { tenant_id: req.user.tenant.id },
        order: [['created_at', 'DESC']],
      });

      const nextNumber = lastCustomer ? 
        parseInt(lastCustomer.customer_code.replace(/\D/g, '')) + 1 : 1;
      customerData.customer_code = `CUST${nextNumber.toString().padStart(4, '0')}`;
    }

    // Check if customer code already exists
    const existingCustomer = await models.Customer.findOne({
      where: {
        tenant_id: req.user.tenant.id,
        customer_code: customerData.customer_code,
      },
    });

    if (existingCustomer) {
      return res.status(409).json({
        success: false,
        message: 'Customer code already exists',
      });
    }

    const customer = await models.Customer.create(customerData);

    // Fetch the created customer with associations
    const createdCustomer = await models.Customer.findByPk(customer.id, {
      include: [
        {
          model: models.Industry,
          as: 'industry',
        },
        {
          model: models.Area,
          as: 'area',
        },
        {
          model: models.Executive,
          as: 'assignedExecutive',
        },
        {
          model: models.User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email'],
        },
      ],
    });

    logger.info('Customer created successfully:', {
      customerId: customer.id,
      customerCode: customer.customer_code,
      companyName: customer.company_name,
      createdBy: req.user.id,
      tenantId: req.user.tenant.id,
    });

    res.status(201).json({
      success: true,
      message: 'Customer created successfully',
      data: { customer: createdCustomer },
    });

  } catch (error) {
    logger.error('Create customer error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create customer',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Update customer
 */
export const updateCustomer = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const customer = await models.Customer.findOne({
      where: {
        id,
        tenant_id: req.user.tenant.id,
      },
    });

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found',
      });
    }

    // Check if customer code is being changed and if it already exists
    if (updateData.customer_code && updateData.customer_code !== customer.customer_code) {
      const existingCustomer = await models.Customer.findOne({
        where: {
          tenant_id: req.user.tenant.id,
          customer_code: updateData.customer_code,
          id: { [Op.ne]: id },
        },
      });

      if (existingCustomer) {
        return res.status(409).json({
          success: false,
          message: 'Customer code already exists',
        });
      }
    }

    await customer.update(updateData);

    // Fetch updated customer with associations
    const updatedCustomer = await models.Customer.findByPk(customer.id, {
      include: [
        {
          model: models.Industry,
          as: 'industry',
        },
        {
          model: models.Area,
          as: 'area',
        },
        {
          model: models.Executive,
          as: 'assignedExecutive',
        },
        {
          model: models.User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email'],
        },
      ],
    });

    logger.info('Customer updated successfully:', {
      customerId: customer.id,
      customerCode: customer.customer_code,
      updatedBy: req.user.id,
    });

    res.json({
      success: true,
      message: 'Customer updated successfully',
      data: { customer: updatedCustomer },
    });

  } catch (error) {
    logger.error('Update customer error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update customer',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Delete customer
 */
export const deleteCustomer = async (req, res) => {
  try {
    const { id } = req.params;

    const customer = await models.Customer.findOne({
      where: {
        id,
        tenant_id: req.user.tenant.id,
      },
    });

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found',
      });
    }

    // Check if customer has related records
    const [serviceCallsCount, salesCount] = await Promise.all([
      models.ServiceCall.count({ where: { customer_id: id } }),
      models.Sale.count({ where: { customer_id: id } }),
    ]);

    if (serviceCallsCount > 0 || salesCount > 0) {
      return res.status(409).json({
        success: false,
        message: 'Cannot delete customer with existing service calls or sales records',
        details: {
          serviceCallsCount,
          salesCount,
        },
      });
    }

    await customer.destroy();

    logger.info('Customer deleted successfully:', {
      customerId: customer.id,
      customerCode: customer.customer_code,
      deletedBy: req.user.id,
    });

    res.json({
      success: true,
      message: 'Customer deleted successfully',
    });

  } catch (error) {
    logger.error('Delete customer error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete customer',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get customer statistics
 */
export const getCustomerStats = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;

    const stats = await Promise.all([
      // Total customers
      models.Customer.count({
        where: { tenant_id: tenantId, is_active: true },
      }),
      
      // Customers by type
      models.Customer.findAll({
        where: { tenant_id: tenantId, is_active: true },
        attributes: [
          'customer_type',
          [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
        ],
        group: ['customer_type'],
        raw: true,
      }),
      
      // Recent customers (last 30 days)
      models.Customer.count({
        where: {
          tenant_id: tenantId,
          is_active: true,
          created_at: {
            [Op.gte]: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          },
        },
      }),
      
      // Customers by industry
      models.Customer.findAll({
        where: { tenant_id: tenantId, is_active: true },
        include: [
          {
            model: models.Industry,
            as: 'industry',
            attributes: ['name'],
          },
        ],
        attributes: [
          [models.sequelize.fn('COUNT', models.sequelize.col('Customer.id')), 'count'],
        ],
        group: ['industry.id', 'industry.name'],
        raw: true,
      }),
    ]);

    const [totalCustomers, customersByType, recentCustomers, customersByIndustry] = stats;

    res.json({
      success: true,
      data: {
        totalCustomers,
        recentCustomers,
        customersByType: customersByType.reduce((acc, item) => {
          acc[item.customer_type] = parseInt(item.count);
          return acc;
        }, {}),
        customersByIndustry: customersByIndustry.map(item => ({
          industry: item['industry.name'] || 'Unknown',
          count: parseInt(item.count),
        })),
      },
    });

  } catch (error) {
    logger.error('Get customer stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch customer statistics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};
