import express from 'express';
import { logger } from './src/utils/logger.js';
import appConfig from './config/app.js';

const testServer = async () => {
  try {
    const app = express();
    
    // Basic middleware
    app.use(express.json());
    
    // Test route
    app.get('/test', (req, res) => {
      res.json({ status: 'ok', message: 'Server is working' });
    });
    
    const PORT = appConfig.app.port;
    const server = app.listen(PORT, () => {
      logger.info(`🚀 Test server running on port ${PORT}`);
    });
    
    // Test for 5 seconds then close
    setTimeout(() => {
      server.close(() => {
        logger.info('Test server closed');
        process.exit(0);
      });
    }, 5000);
    
  } catch (error) {
    logger.error('Test server failed:', error);
    process.exit(1);
  }
};

testServer();
