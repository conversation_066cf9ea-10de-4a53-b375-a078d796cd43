# 🗄️ SaaS CRM for Tally Resellers - Database Design

## 📋 Database Overview

### Database System
- **Database**: PostgreSQL 14+
- **Architecture**: Multi-Tenant with Row Level Security (RLS)
- **Schema**: Single database with tenant isolation
- **Encoding**: UTF-8
- **Timezone**: UTC

### Multi-Tenant Strategy
- **Approach**: Shared database with tenant_id column
- **Isolation**: Row Level Security (RLS) policies
- **Scalability**: Horizontal partitioning ready
- **Security**: Tenant data completely isolated

### Design Principles
- **Normalization**: 3NF with selective denormalization for performance
- **Indexing**: Strategic indexes for query optimization
- **Constraints**: Comprehensive data integrity constraints
- **Audit Trail**: Created/updated timestamps on all tables
- **Soft Deletes**: Logical deletion with deleted_at timestamps

## 🏗️ Core Tables Structure

### 1. Organizations & Users

#### organizations
```sql
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    subdomain VARCHAR(100) UNIQUE NOT NULL,
    contact_email VARCHAR(255) NOT NULL,
    contact_phone VARCHAR(20),
    address TEXT,
    subscription_plan VARCHAR(50) DEFAULT 'basic',
    subscription_status VARCHAR(20) DEFAULT 'active',
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

#### users
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    username VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    role_id UUID REFERENCES staff_roles(id),
    is_active BOOLEAN DEFAULT true,
    last_login_at TIMESTAMP WITH TIME ZONE,
    email_verified_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

#### user_permissions
```sql
CREATE TABLE user_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    user_id UUID NOT NULL REFERENCES users(id),
    module_name VARCHAR(100) NOT NULL,
    can_view BOOLEAN DEFAULT false,
    can_add BOOLEAN DEFAULT false,
    can_edit BOOLEAN DEFAULT false,
    can_delete BOOLEAN DEFAULT false,
    can_export BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 2. Master Data Tables

#### licence_editions
```sql
CREATE TABLE licence_editions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

#### designations
```sql
CREATE TABLE designations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_mandatory BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

#### tally_products
```sql
CREATE TABLE tally_products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

#### staff_roles
```sql
CREATE TABLE staff_roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

#### executives
```sql
CREATE TABLE executives (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(20),
    role_id UUID REFERENCES staff_roles(id),
    user_id UUID REFERENCES users(id),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

#### industries
```sql
CREATE TABLE industries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

#### areas
```sql
CREATE TABLE areas (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

#### nature_of_issues
```sql
CREATE TABLE nature_of_issues (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

#### additional_services
```sql
CREATE TABLE additional_services (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

#### call_statuses
```sql
CREATE TABLE call_statuses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

### 3. Customer Management Tables

#### customers
```sql
CREATE TABLE customers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    customer_name VARCHAR(255) NOT NULL,
    tally_serial_no VARCHAR(100),
    product_id UUID REFERENCES tally_products(id),
    licence_edition_id UUID REFERENCES licence_editions(id),
    location_id UUID REFERENCES areas(id),
    profile_status VARCHAR(50) DEFAULT 'FOLLOW UP',
    follow_up_executive_id UUID REFERENCES executives(id),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    gst_no VARCHAR(15),
    tdl_addons TEXT,
    remarks TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

#### customer_contacts
```sql
CREATE TABLE customer_contacts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    customer_id UUID NOT NULL REFERENCES customers(id),
    designation_id UUID REFERENCES designations(id),
    contact_person VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(255),
    is_primary BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

#### customer_mobile_numbers
```sql
CREATE TABLE customer_mobile_numbers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    customer_contact_id UUID NOT NULL REFERENCES customer_contacts(id),
    mobile_number VARCHAR(15) NOT NULL,
    is_primary BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### customer_tss
```sql
CREATE TABLE customer_tss (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    customer_id UUID NOT NULL REFERENCES customers(id),
    status VARCHAR(20) NOT NULL CHECK (status IN ('Active', 'Inactive')),
    expiry_date DATE,
    admin_email VARCHAR(255),
    expired_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### customer_amc
```sql
CREATE TABLE customer_amc (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    customer_id UUID NOT NULL REFERENCES customers(id),
    amc_status VARCHAR(10) NOT NULL CHECK (amc_status IN ('YES', 'NO')),
    from_date DATE,
    to_date DATE,
    renewal_date DATE,
    no_of_visits INTEGER,
    current_year_amount DECIMAL(10,2),
    last_year_amount DECIMAL(10,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### customer_additional_configs
```sql
CREATE TABLE customer_additional_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    customer_id UUID NOT NULL REFERENCES customers(id),
    additional_service_id UUID NOT NULL REFERENCES additional_services(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 4. Services Management Tables

#### service_calls
```sql
CREATE TABLE service_calls (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    customer_id UUID NOT NULL REFERENCES customers(id),
    contact_person_id UUID REFERENCES customer_contacts(id),
    contact_mobile_id UUID REFERENCES customer_mobile_numbers(id),
    tally_serial_number VARCHAR(100),
    call_type VARCHAR(20) NOT NULL CHECK (call_type IN ('AMC Call', 'Free Call', 'Per Call')),
    service_type VARCHAR(20) NOT NULL CHECK (service_type IN ('Online', 'Onsite')),
    booking_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    start_time TIMESTAMP WITH TIME ZONE,
    end_time TIMESTAMP WITH TIME ZONE,
    call_status_id UUID REFERENCES call_statuses(id),
    assigned_executive_id UUID REFERENCES executives(id),
    nature_of_issue_id UUID REFERENCES nature_of_issues(id),
    service_charges DECIMAL(10,2),
    remarks TEXT,
    follow_up_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

### 5. Sales Management Tables

#### sales
```sql
CREATE TABLE sales (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    customer_id UUID REFERENCES customers(id),
    customer_name VARCHAR(255) NOT NULL,
    sale_date DATE NOT NULL,
    product_id UUID REFERENCES tally_products(id),
    amount DECIMAL(10,2) NOT NULL,
    referral_name VARCHAR(255),
    referral_code VARCHAR(50),
    follow_up_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

## 🔐 Security Implementation

### Row Level Security (RLS) Policies

#### Enable RLS on all tables
```sql
-- Enable RLS on all tenant tables
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
-- ... (enable for all tables)
```

#### Create RLS Policies
```sql
-- Users can only see their organization's data
CREATE POLICY tenant_isolation_users ON users
    FOR ALL TO authenticated_user
    USING (organization_id = current_setting('app.current_organization_id')::UUID);

-- Customers policy
CREATE POLICY tenant_isolation_customers ON customers
    FOR ALL TO authenticated_user
    USING (organization_id = current_setting('app.current_organization_id')::UUID);

-- Apply similar policies to all tables
```

### Database Roles and Permissions
```sql
-- Create application role
CREATE ROLE app_user;

-- Grant necessary permissions
GRANT CONNECT ON DATABASE tallycrm_dev TO app_user;
GRANT USAGE ON SCHEMA public TO app_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO app_user;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO app_user;
```

## 📊 Indexes for Performance

### Primary Indexes
```sql
-- Composite indexes for tenant isolation
CREATE INDEX idx_users_org_email ON users(organization_id, email);
CREATE INDEX idx_customers_org_name ON customers(organization_id, customer_name);
CREATE INDEX idx_service_calls_org_customer ON service_calls(organization_id, customer_id);
CREATE INDEX idx_sales_org_date ON sales(organization_id, sale_date);

-- Search indexes
CREATE INDEX idx_customers_search ON customers USING gin(to_tsvector('english', customer_name));
CREATE INDEX idx_customers_tally_serial ON customers(tally_serial_no);

-- Foreign key indexes
CREATE INDEX idx_customers_product ON customers(product_id);
CREATE INDEX idx_customers_location ON customers(location_id);
CREATE INDEX idx_service_calls_status ON service_calls(call_status_id);
CREATE INDEX idx_service_calls_executive ON service_calls(assigned_executive_id);

-- Date range indexes
CREATE INDEX idx_service_calls_booking_time ON service_calls(booking_time);
CREATE INDEX idx_sales_date_range ON sales(sale_date);
CREATE INDEX idx_customer_amc_dates ON customer_amc(from_date, to_date);
```

### Performance Optimization Indexes
```sql
-- Partial indexes for active records
CREATE INDEX idx_active_customers ON customers(organization_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_active_service_calls ON service_calls(organization_id) WHERE deleted_at IS NULL;

-- Covering indexes for common queries
CREATE INDEX idx_customers_list_covering ON customers(organization_id, customer_name) 
    INCLUDE (tally_serial_no, profile_status, created_at) WHERE deleted_at IS NULL;
```

## 🌱 Seed Data Scripts

### Default Organizations and Roles
```sql
-- Insert default organization (for development)
INSERT INTO organizations (id, name, subdomain, contact_email) VALUES
('********-0000-0000-0000-000*********', 'Demo Organization', 'demo', '<EMAIL>');

-- Insert default staff roles
INSERT INTO staff_roles (organization_id, name, description) VALUES
('********-0000-0000-0000-000*********', 'Super Admin', 'Full system access'),
('********-0000-0000-0000-000*********', 'Admin', 'Organization admin access'),
('********-0000-0000-0000-000*********', 'Manager', 'Management level access'),
('********-0000-0000-0000-000*********', 'Executive', 'Standard user access'),
('********-0000-0000-0000-000*********', 'Support', 'Support team access');
```

### Master Data Seeds
```sql
-- License Editions
INSERT INTO licence_editions (organization_id, name, description) VALUES
('********-0000-0000-0000-000*********', 'Silver', 'Basic Tally license'),
('********-0000-0000-0000-000*********', 'Gold', 'Advanced Tally license'),
('********-0000-0000-0000-000*********', 'Platinum', 'Premium Tally license');

-- Designations
INSERT INTO designations (organization_id, name, description, is_mandatory) VALUES
('********-0000-0000-0000-000*********', 'Owner', 'Business owner', true),
('********-0000-0000-0000-000*********', 'Manager', 'Business manager', false),
('********-0000-0000-0000-000*********', 'Accountant', 'Company accountant', true),
('********-0000-0000-0000-000*********', 'Auditor', 'External auditor', false);

-- Call Statuses
INSERT INTO call_statuses (organization_id, name, description) VALUES
('********-0000-0000-0000-000*********', 'Pending', 'Call is pending'),
('********-0000-0000-0000-000*********', 'In Progress', 'Call is in progress'),
('********-0000-0000-0000-000*********', 'Completed', 'Call completed successfully'),
('********-0000-0000-0000-000*********', 'Cancelled', 'Call was cancelled'),
('********-0000-0000-0000-000*********', 'Follow-up', 'Requires follow-up'),
('********-0000-0000-0000-000*********', 'Onsite Visit', 'Requires onsite visit'),
('********-0000-0000-0000-000*********', 'Online Call', 'Online support call');
```

## 🔧 Database Maintenance

### Backup Strategy
```sql
-- Daily backup script
pg_dump -h localhost -U tallycrm_dev -d tallycrm_dev -f backup_$(date +%Y%m%d).sql

-- Point-in-time recovery setup
archive_mode = on
archive_command = 'cp %p /var/lib/postgresql/archive/%f'
```

### Monitoring Queries
```sql
-- Check table sizes
SELECT schemaname, tablename, pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables WHERE schemaname = 'public' ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Check index usage
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes ORDER BY idx_scan DESC;

-- Monitor slow queries
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10;
```

**Next Steps**: Proceed to [API Documentation](API_DOCUMENTATION.md) for complete API reference and endpoint details.
