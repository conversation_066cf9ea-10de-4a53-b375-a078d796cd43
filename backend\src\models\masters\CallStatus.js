import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const CallStatus = sequelize.define('CallStatus', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [2, 50],
      },
    },
    code: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        len: [2, 20],
      },
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    category: {
      type: DataTypes.ENUM('open', 'in_progress', 'resolved', 'closed', 'cancelled'),
      allowNull: false,
      defaultValue: 'open',
    },
    color: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: '#6c757d',
      comment: 'Color code for UI display',
    },
    is_final: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Whether this is a final status (no further changes allowed)',
    },
    requires_approval: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Whether changing to this status requires approval',
    },
    auto_close_after_days: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Auto-close call after specified days in this status',
    },
    is_billable: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      comment: 'Whether calls in this status are billable',
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    sort_order: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'call_statuses',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['code'],
        unique: true,
      },
      {
        fields: ['category'],
      },
      {
        fields: ['is_final'],
      },
      {
        fields: ['is_active'],
      },
      {
        fields: ['sort_order'],
      },
    ],
  });

  // Class methods
  CallStatus.getDefaultStatuses = function() {
    return [
      {
        name: 'Open',
        code: 'OPEN',
        description: 'Call has been logged and is waiting to be assigned',
        category: 'open',
        color: '#17a2b8',
        is_final: false,
        requires_approval: false,
        auto_close_after_days: null,
        is_billable: false,
        sort_order: 1,
      },
      {
        name: 'Assigned',
        code: 'ASSIGNED',
        description: 'Call has been assigned to an executive',
        category: 'open',
        color: '#ffc107',
        is_final: false,
        requires_approval: false,
        auto_close_after_days: null,
        is_billable: false,
        sort_order: 2,
      },
      {
        name: 'In Progress',
        code: 'IN_PROGRESS',
        description: 'Executive is working on the call',
        category: 'in_progress',
        color: '#fd7e14',
        is_final: false,
        requires_approval: false,
        auto_close_after_days: null,
        is_billable: true,
        sort_order: 3,
      },
      {
        name: 'On Hold',
        code: 'ON_HOLD',
        description: 'Call is temporarily on hold',
        category: 'in_progress',
        color: '#6f42c1',
        is_final: false,
        requires_approval: false,
        auto_close_after_days: null,
        is_billable: false,
        sort_order: 4,
      },
      {
        name: 'Pending Customer',
        code: 'PENDING_CUSTOMER',
        description: 'Waiting for customer response or action',
        category: 'in_progress',
        color: '#e83e8c',
        is_final: false,
        requires_approval: false,
        auto_close_after_days: 7,
        is_billable: false,
        sort_order: 5,
      },
      {
        name: 'Resolved',
        code: 'RESOLVED',
        description: 'Issue has been resolved',
        category: 'resolved',
        color: '#28a745',
        is_final: false,
        requires_approval: false,
        auto_close_after_days: 3,
        is_billable: true,
        sort_order: 6,
      },
      {
        name: 'Closed',
        code: 'CLOSED',
        description: 'Call has been closed successfully',
        category: 'closed',
        color: '#6c757d',
        is_final: true,
        requires_approval: false,
        auto_close_after_days: null,
        is_billable: true,
        sort_order: 7,
      },
      {
        name: 'Cancelled',
        code: 'CANCELLED',
        description: 'Call has been cancelled',
        category: 'cancelled',
        color: '#dc3545',
        is_final: true,
        requires_approval: true,
        auto_close_after_days: null,
        is_billable: false,
        sort_order: 8,
      },
      {
        name: 'Escalated',
        code: 'ESCALATED',
        description: 'Call has been escalated to higher level',
        category: 'in_progress',
        color: '#dc3545',
        is_final: false,
        requires_approval: true,
        auto_close_after_days: null,
        is_billable: true,
        sort_order: 9,
      },
      {
        name: 'Rescheduled',
        code: 'RESCHEDULED',
        description: 'Call has been rescheduled',
        category: 'open',
        color: '#20c997',
        is_final: false,
        requires_approval: false,
        auto_close_after_days: null,
        is_billable: false,
        sort_order: 10,
      },
    ];
  };

  // Instance methods
  CallStatus.prototype.canTransitionTo = function(targetStatus) {
    const validTransitions = {
      'OPEN': ['ASSIGNED', 'CANCELLED'],
      'ASSIGNED': ['IN_PROGRESS', 'ON_HOLD', 'RESCHEDULED', 'CANCELLED'],
      'IN_PROGRESS': ['RESOLVED', 'ON_HOLD', 'PENDING_CUSTOMER', 'ESCALATED', 'CANCELLED'],
      'ON_HOLD': ['IN_PROGRESS', 'ASSIGNED', 'CANCELLED'],
      'PENDING_CUSTOMER': ['IN_PROGRESS', 'RESOLVED', 'CANCELLED'],
      'RESOLVED': ['CLOSED', 'IN_PROGRESS'],
      'ESCALATED': ['IN_PROGRESS', 'RESOLVED', 'CANCELLED'],
      'RESCHEDULED': ['ASSIGNED', 'IN_PROGRESS', 'CANCELLED'],
      'CLOSED': [], // Final status
      'CANCELLED': [], // Final status
    };

    return validTransitions[this.code]?.includes(targetStatus.code) || false;
  };

  CallStatus.prototype.isOpen = function() {
    return ['open', 'in_progress'].includes(this.category);
  };

  CallStatus.prototype.isClosed = function() {
    return ['resolved', 'closed', 'cancelled'].includes(this.category);
  };

  // Associations
  CallStatus.associate = function(models) {
    CallStatus.hasMany(models.ServiceCall, {
      foreignKey: 'status_id',
      as: 'serviceCalls',
    });
  };

  return CallStatus;
}
