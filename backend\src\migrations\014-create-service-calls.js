import { DataTypes } from 'sequelize';

export const up = async (queryInterface) => {
  // Create service_calls table
  await queryInterface.createTable('service_calls', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    call_number: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    customer_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'customers',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    contact_person_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'customer_contacts',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    tss_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'customer_tss',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    amc_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'customer_amc',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    call_type: {
      type: DataTypes.ENUM('online', 'onsite', 'phone', 'email'),
      allowNull: false,
      defaultValue: 'online',
    },
    priority: {
      type: DataTypes.ENUM('low', 'medium', 'high', 'critical'),
      allowNull: false,
      defaultValue: 'medium',
    },
    nature_of_issue_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'nature_of_issues',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    status_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'call_statuses',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'RESTRICT',
    },
    area_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'areas',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    assigned_to: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'executives',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    created_by: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'RESTRICT',
    },
    subject: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    customer_reported_issue: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    actual_issue_found: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    solution_provided: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    call_date: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    scheduled_date: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    started_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    completed_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    closed_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    estimated_hours: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
    },
    actual_hours: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
    },
    billable_hours: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
    },
    hourly_rate: {
      type: DataTypes.DECIMAL(8, 2),
      allowNull: true,
    },
    service_charges: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    travel_charges: {
      type: DataTypes.DECIMAL(8, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    total_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    is_billable: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    is_under_amc: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    customer_satisfaction: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    customer_feedback: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    internal_notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    follow_up_required: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    follow_up_date: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    follow_up_notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    attachments: {
      type: DataTypes.JSONB,
      defaultValue: [],
    },
    tags: {
      type: DataTypes.JSONB,
      defaultValue: [],
    },
    custom_fields: {
      type: DataTypes.JSONB,
      defaultValue: {},
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  });

  // Add indexes
  await queryInterface.addIndex('service_calls', ['tenant_id']);
  await queryInterface.addIndex('service_calls', ['call_number'], { unique: true });
  await queryInterface.addIndex('service_calls', ['customer_id']);
  await queryInterface.addIndex('service_calls', ['call_type']);
  await queryInterface.addIndex('service_calls', ['priority']);
  await queryInterface.addIndex('service_calls', ['status_id']);
  await queryInterface.addIndex('service_calls', ['assigned_to']);
  await queryInterface.addIndex('service_calls', ['created_by']);
  await queryInterface.addIndex('service_calls', ['call_date']);
  await queryInterface.addIndex('service_calls', ['scheduled_date']);
  await queryInterface.addIndex('service_calls', ['is_billable']);
  await queryInterface.addIndex('service_calls', ['is_under_amc']);
  await queryInterface.addIndex('service_calls', ['follow_up_required']);
  await queryInterface.addIndex('service_calls', ['follow_up_date']);
};

export const down = async (queryInterface) => {
  await queryInterface.dropTable('service_calls');
};
