import axios from 'axios';
import { API_CONFIG, STORAGE_KEYS, FEATURES } from '../utils/constants';

// Create axios instance
const api = axios.create({
  baseURL: API_CONFIG.BASE_URL,
  timeout: API_CONFIG.TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem(STORAGE_KEYS.TOKEN);
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // Handle 401 errors (unauthorized)
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Try to refresh token
        const refreshToken = localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
        if (refreshToken) {
          const response = await axios.post(`${API_CONFIG.BASE_URL}/auth/refresh`, {
            refreshToken,
          });

          const { token } = response.data;
          localStorage.setItem(STORAGE_KEYS.TOKEN, token);

          // Retry original request
          originalRequest.headers.Authorization = `Bearer ${token}`;
          return api(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem(STORAGE_KEYS.TOKEN);
        localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
        localStorage.removeItem(STORAGE_KEYS.USER);
        window.location.href = '/auth/login';
      }
    }

    return Promise.reject(error);
  }
);

// Mock API responses when ENABLE_MOCK_API is true
const mockResponses = {
  // Auth endpoints
  'POST /auth/login': {
    data: {
      token: 'mock-jwt-token',
      refreshToken: 'mock-refresh-token',
      user: {
        id: 1,
        name: 'John Doe',
        email: '<EMAIL>',
        role: 'admin',
        permissions: ['read', 'write', 'delete'],
      },
    },
  },
  'POST /auth/refresh': {
    data: {
      token: 'new-mock-jwt-token',
    },
  },
  'POST /auth/logout': {
    data: { message: 'Logged out successfully' },
  },

  // Dashboard endpoints
  'GET /dashboard/stats': {
    data: {
      totalCustomers: 1234,
      activeServices: 89,
      monthlyRevenue: 245000,
      pendingTasks: 23,
    },
  },

  // Customer endpoints
  'GET /customers': {
    data: {
      customers: [],
      pagination: {
        page: 1,
        limit: 10,
        total: 0,
        pages: 0,
      },
    },
  },
};

// Mock API function
const mockApiCall = (method, url) => {
  const key = `${method.toUpperCase()} ${url}`;
  const mockResponse = mockResponses[key];
  
  if (mockResponse) {
    return Promise.resolve({ data: mockResponse.data });
  }
  
  return Promise.reject(new Error(`Mock API: No mock response found for ${key}`));
};

// API service functions
export const apiService = {
  // Generic HTTP methods
  get: (url, config = {}) => {
    if (FEATURES.ENABLE_MOCK_API) {
      return mockApiCall('GET', url);
    }
    return api.get(url, config);
  },

  post: (url, data = {}, config = {}) => {
    if (FEATURES.ENABLE_MOCK_API) {
      return mockApiCall('POST', url);
    }
    return api.post(url, data, config);
  },

  put: (url, data = {}, config = {}) => {
    if (FEATURES.ENABLE_MOCK_API) {
      return mockApiCall('PUT', url);
    }
    return api.put(url, data, config);
  },

  patch: (url, data = {}, config = {}) => {
    if (FEATURES.ENABLE_MOCK_API) {
      return mockApiCall('PATCH', url);
    }
    return api.patch(url, data, config);
  },

  delete: (url, config = {}) => {
    if (FEATURES.ENABLE_MOCK_API) {
      return mockApiCall('DELETE', url);
    }
    return api.delete(url, config);
  },

  // File upload
  upload: (url, formData, config = {}) => {
    const uploadConfig = {
      ...config,
      headers: {
        ...config.headers,
        'Content-Type': 'multipart/form-data',
      },
    };
    
    if (FEATURES.ENABLE_MOCK_API) {
      return Promise.resolve({ data: { message: 'File uploaded successfully' } });
    }
    
    return api.post(url, formData, uploadConfig);
  },
};

// Auth API
export const authAPI = {
  login: (credentials) => apiService.post('/auth/login', credentials),
  logout: () => apiService.post('/auth/logout'),
  refresh: (refreshToken) => apiService.post('/auth/refresh', { refreshToken }),
  register: (userData) => apiService.post('/auth/register', userData),
  forgotPassword: (email) => apiService.post('/auth/forgot-password', { email }),
  resetPassword: (token, password) => apiService.post('/auth/reset-password', { token, password }),
  verifyEmail: (token) => apiService.post('/auth/verify-email', { token }),
};

// Dashboard API
export const dashboardAPI = {
  getStats: () => apiService.get('/dashboard/stats'),
  getRecentActivities: () => apiService.get('/dashboard/activities'),
  getChartData: (type) => apiService.get(`/dashboard/charts/${type}`),
};

// Customer API
export const customerAPI = {
  getAll: (params = {}) => apiService.get('/customers', { params }),
  getById: (id) => apiService.get(`/customers/${id}`),
  create: (data) => apiService.post('/customers', data),
  update: (id, data) => apiService.put(`/customers/${id}`, data),
  delete: (id) => apiService.delete(`/customers/${id}`),
  search: (query) => apiService.get('/customers/search', { params: { q: query } }),
};

export default api;
