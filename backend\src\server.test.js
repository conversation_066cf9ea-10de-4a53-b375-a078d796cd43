import { describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import request from 'supertest';
import app from './server.js';

describe('TallyCRM Backend Server', () => {
  let server;

  beforeAll(() => {
    // Start server for testing
    const PORT = process.env.TEST_PORT || 5001;
    server = app.listen(PORT);
  });

  afterAll(() => {
    // Close server after tests
    if (server) {
      server.close();
    }
  });

  describe('Health Endpoints', () => {
    it('should return 200 for basic health check', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      expect(response.body).toHaveProperty('status', 'ok');
      expect(response.body).toHaveProperty('timestamp');
    });

    it('should return detailed health information', async () => {
      const response = await request(app)
        .get('/health/detailed')
        .expect(200);

      expect(response.body).toHaveProperty('status', 'ok');
      expect(response.body).toHaveProperty('uptime');
      expect(response.body).toHaveProperty('environment');
      expect(response.body).toHaveProperty('version');
      expect(response.body).toHaveProperty('system');
    });

    it('should return readiness status', async () => {
      const response = await request(app)
        .get('/health/ready')
        .expect(200);

      expect(response.body).toHaveProperty('status', 'ready');
      expect(response.body).toHaveProperty('checks');
    });

    it('should return liveness status', async () => {
      const response = await request(app)
        .get('/health/live')
        .expect(200);

      expect(response.body).toHaveProperty('status', 'alive');
      expect(response.body).toHaveProperty('pid');
    });
  });

  describe('Auth Endpoints', () => {
    it('should handle login request', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const response = await request(app)
        .post('/api/v1/auth/login')
        .send(loginData)
        .expect(200);

      expect(response.body).toHaveProperty('status', 'success');
      expect(response.body).toHaveProperty('token');
      expect(response.body).toHaveProperty('user');
    });

    it('should validate login input', async () => {
      const invalidLoginData = {
        email: 'invalid-email',
        password: '123', // too short
      };

      const response = await request(app)
        .post('/api/v1/auth/login')
        .send(invalidLoginData)
        .expect(400);

      expect(response.body).toHaveProperty('status', 'error');
    });

    it('should handle logout request', async () => {
      const response = await request(app)
        .post('/api/v1/auth/logout')
        .expect(200);

      expect(response.body).toHaveProperty('status', 'success');
    });

    it('should handle token refresh', async () => {
      const refreshData = {
        refreshToken: 'mock-refresh-token',
      };

      const response = await request(app)
        .post('/api/v1/auth/refresh')
        .send(refreshData)
        .expect(200);

      expect(response.body).toHaveProperty('status', 'success');
      expect(response.body).toHaveProperty('token');
    });
  });

  describe('User Endpoints', () => {
    it('should get users list', async () => {
      const response = await request(app)
        .get('/api/v1/users')
        .expect(200);

      expect(response.body).toHaveProperty('status', 'success');
      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('pagination');
    });

    it('should get user by ID', async () => {
      const response = await request(app)
        .get('/api/v1/users/1')
        .expect(200);

      expect(response.body).toHaveProperty('status', 'success');
      expect(response.body).toHaveProperty('data');
    });

    it('should validate user ID parameter', async () => {
      const response = await request(app)
        .get('/api/v1/users/invalid')
        .expect(400);

      expect(response.body).toHaveProperty('status', 'error');
    });

    it('should create new user', async () => {
      const userData = {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
        role: 'user',
      };

      const response = await request(app)
        .post('/api/v1/users')
        .send(userData)
        .expect(201);

      expect(response.body).toHaveProperty('status', 'success');
      expect(response.body).toHaveProperty('data');
    });

    it('should validate user creation data', async () => {
      const invalidUserData = {
        name: 'A', // too short
        email: 'invalid-email',
        password: '123', // too short
        role: 'invalid-role',
      };

      const response = await request(app)
        .post('/api/v1/users')
        .send(invalidUserData)
        .expect(400);

      expect(response.body).toHaveProperty('status', 'error');
    });
  });

  describe('Error Handling', () => {
    it('should return 404 for non-existent routes', async () => {
      const response = await request(app)
        .get('/api/v1/non-existent')
        .expect(404);

      expect(response.body).toHaveProperty('status', 'error');
    });

    it('should handle malformed JSON', async () => {
      const response = await request(app)
        .post('/api/v1/auth/login')
        .set('Content-Type', 'application/json')
        .send('invalid json')
        .expect(400);
    });
  });
});
