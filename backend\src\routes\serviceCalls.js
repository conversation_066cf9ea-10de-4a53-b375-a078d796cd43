import express from 'express';
import { body, query, param } from 'express-validator';
import { validate } from '../middleware/validation.js';
import { authenticateToken, requirePermission, requireTenantAccess } from '../middleware/auth.js';
import {
  getServiceCalls,
  getServiceCallById,
  createServiceCall,
  updateServiceCall,
  getServiceCallStats,
} from '../controllers/serviceCallController.js';

const router = express.Router();

// Apply authentication and tenant access to all routes
router.use(authenticateToken);
router.use(requireTenantAccess);

/**
 * @route   GET /api/service-calls
 * @desc    Get all service calls with pagination and filters
 * @access  Private (requires service_calls.read permission)
 */
router.get('/', [
  requirePermission('service_calls.read'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('search')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search term must be between 1 and 100 characters'),
  query('customerId')
    .optional()
    .isUUID()
    .withMessage('Customer ID must be a valid UUID'),
  query('statusId')
    .optional()
    .isUUID()
    .withMessage('Status ID must be a valid UUID'),
  query('assignedTo')
    .optional()
    .isUUID()
    .withMessage('Assigned to must be a valid UUID'),
  query('callType')
    .optional()
    .isIn(['online', 'onsite', 'phone', 'email'])
    .withMessage('Invalid call type'),
  query('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'critical'])
    .withMessage('Invalid priority'),
  query('isUnderAmc')
    .optional()
    .isBoolean()
    .withMessage('isUnderAmc must be a boolean'),
  query('dateFrom')
    .optional()
    .isISO8601()
    .withMessage('Date from must be a valid date'),
  query('dateTo')
    .optional()
    .isISO8601()
    .withMessage('Date to must be a valid date'),
  query('sortBy')
    .optional()
    .isIn(['created_at', 'updated_at', 'call_date', 'scheduled_date', 'priority'])
    .withMessage('Invalid sort field'),
  query('sortOrder')
    .optional()
    .isIn(['ASC', 'DESC'])
    .withMessage('Sort order must be ASC or DESC'),
  validate,
], getServiceCalls);

/**
 * @route   GET /api/service-calls/stats
 * @desc    Get service call statistics
 * @access  Private (requires service_calls.read permission)
 */
router.get('/stats', [
  requirePermission('service_calls.read'),
], getServiceCallStats);

/**
 * @route   GET /api/service-calls/:id
 * @desc    Get service call by ID
 * @access  Private (requires service_calls.read permission)
 */
router.get('/:id', [
  requirePermission('service_calls.read'),
  param('id')
    .isUUID()
    .withMessage('Service call ID must be a valid UUID'),
  validate,
], getServiceCallById);

/**
 * @route   POST /api/service-calls
 * @desc    Create new service call
 * @access  Private (requires service_calls.create permission)
 */
router.post('/', [
  requirePermission('service_calls.create'),
  body('customer_id')
    .isUUID()
    .withMessage('Customer ID must be a valid UUID'),
  body('subject')
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage('Subject must be between 5 and 200 characters'),
  body('description')
    .trim()
    .isLength({ min: 10, max: 2000 })
    .withMessage('Description must be between 10 and 2000 characters'),
  body('call_number')
    .optional()
    .trim()
    .isLength({ min: 2, max: 20 })
    .withMessage('Call number must be between 2 and 20 characters'),
  body('contact_person_id')
    .optional()
    .isUUID()
    .withMessage('Contact person ID must be a valid UUID'),
  body('tss_id')
    .optional()
    .isUUID()
    .withMessage('TSS ID must be a valid UUID'),
  body('amc_id')
    .optional()
    .isUUID()
    .withMessage('AMC ID must be a valid UUID'),
  body('call_type')
    .optional()
    .isIn(['online', 'onsite', 'phone', 'email'])
    .withMessage('Invalid call type'),
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'critical'])
    .withMessage('Invalid priority'),
  body('nature_of_issue_id')
    .optional()
    .isUUID()
    .withMessage('Nature of issue ID must be a valid UUID'),
  body('area_id')
    .optional()
    .isUUID()
    .withMessage('Area ID must be a valid UUID'),
  body('assigned_to')
    .optional()
    .isUUID()
    .withMessage('Assigned to must be a valid UUID'),
  body('scheduled_date')
    .optional()
    .isISO8601()
    .withMessage('Scheduled date must be a valid date'),
  body('estimated_hours')
    .optional()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('Estimated hours must be a valid decimal number'),
  body('is_billable')
    .optional()
    .isBoolean()
    .withMessage('isBillable must be a boolean'),
  body('items')
    .optional()
    .isArray()
    .withMessage('Items must be an array'),
  body('items.*.item_type')
    .optional()
    .isIn(['product', 'service', 'additional_service'])
    .withMessage('Invalid item type'),
  body('items.*.description')
    .optional()
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Item description must be between 2 and 200 characters'),
  body('items.*.quantity')
    .optional()
    .isDecimal({ decimal_digits: '0,3' })
    .withMessage('Item quantity must be a valid decimal number'),
  body('items.*.rate')
    .optional()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('Item rate must be a valid decimal number'),
  validate,
], createServiceCall);

/**
 * @route   PUT /api/service-calls/:id
 * @desc    Update service call
 * @access  Private (requires service_calls.update permission)
 */
router.put('/:id', [
  requirePermission('service_calls.update'),
  param('id')
    .isUUID()
    .withMessage('Service call ID must be a valid UUID'),
  body('subject')
    .optional()
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage('Subject must be between 5 and 200 characters'),
  body('description')
    .optional()
    .trim()
    .isLength({ min: 10, max: 2000 })
    .withMessage('Description must be between 10 and 2000 characters'),
  body('customer_reported_issue')
    .optional()
    .trim()
    .isLength({ max: 2000 })
    .withMessage('Customer reported issue must be less than 2000 characters'),
  body('actual_issue_found')
    .optional()
    .trim()
    .isLength({ max: 2000 })
    .withMessage('Actual issue found must be less than 2000 characters'),
  body('solution_provided')
    .optional()
    .trim()
    .isLength({ max: 2000 })
    .withMessage('Solution provided must be less than 2000 characters'),
  body('status_id')
    .optional()
    .isUUID()
    .withMessage('Status ID must be a valid UUID'),
  body('assigned_to')
    .optional()
    .isUUID()
    .withMessage('Assigned to must be a valid UUID'),
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'critical'])
    .withMessage('Invalid priority'),
  body('scheduled_date')
    .optional()
    .isISO8601()
    .withMessage('Scheduled date must be a valid date'),
  body('started_at')
    .optional()
    .isISO8601()
    .withMessage('Started at must be a valid date'),
  body('completed_at')
    .optional()
    .isISO8601()
    .withMessage('Completed at must be a valid date'),
  body('actual_hours')
    .optional()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('Actual hours must be a valid decimal number'),
  body('billable_hours')
    .optional()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('Billable hours must be a valid decimal number'),
  body('hourly_rate')
    .optional()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('Hourly rate must be a valid decimal number'),
  body('service_charges')
    .optional()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('Service charges must be a valid decimal number'),
  body('travel_charges')
    .optional()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('Travel charges must be a valid decimal number'),
  body('customer_satisfaction')
    .optional()
    .isInt({ min: 1, max: 5 })
    .withMessage('Customer satisfaction must be between 1 and 5'),
  body('customer_feedback')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Customer feedback must be less than 1000 characters'),
  body('internal_notes')
    .optional()
    .trim()
    .isLength({ max: 2000 })
    .withMessage('Internal notes must be less than 2000 characters'),
  body('follow_up_required')
    .optional()
    .isBoolean()
    .withMessage('Follow up required must be a boolean'),
  body('follow_up_date')
    .optional()
    .isISO8601()
    .withMessage('Follow up date must be a valid date'),
  body('follow_up_notes')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Follow up notes must be less than 1000 characters'),
  validate,
], updateServiceCall);

export default router;
