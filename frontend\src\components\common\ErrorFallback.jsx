import React from 'react';
import { Container, <PERSON>, <PERSON>, <PERSON>, But<PERSON> } from 'react-bootstrap';

const ErrorFallback = ({ error, resetErrorBoundary }) => {
  return (
    <Container className="mt-5">
      <Row className="justify-content-center">
        <Col md={8} lg={6}>
          <Card className="text-center">
            <Card.Body className="p-5">
              <div className="mb-4">
                <i className="bi bi-exclamation-triangle text-danger" style={{ fontSize: '4rem' }}></i>
              </div>
              
              <h2 className="text-danger mb-3">Oops! Something went wrong</h2>
              
              <p className="text-muted mb-4">
                We're sorry, but something unexpected happened. Our team has been notified and is working to fix the issue.
              </p>
              
              {process.env.NODE_ENV === 'development' && (
                <div className="mb-4">
                  <details className="text-start">
                    <summary className="btn btn-outline-secondary btn-sm mb-3">
                      Show Error Details
                    </summary>
                    <pre className="bg-light p-3 rounded text-danger small text-start">
                      {error.message}
                      {error.stack && (
                        <>
                          <br />
                          <br />
                          {error.stack}
                        </>
                      )}
                    </pre>
                  </details>
                </div>
              )}
              
              <div className="d-flex gap-2 justify-content-center">
                <Button 
                  variant="primary" 
                  onClick={resetErrorBoundary}
                  className="px-4"
                >
                  <i className="bi bi-arrow-clockwise me-2"></i>
                  Try Again
                </Button>
                
                <Button 
                  variant="outline-secondary" 
                  onClick={() => window.location.href = '/'}
                  className="px-4"
                >
                  <i className="bi bi-house me-2"></i>
                  Go Home
                </Button>
              </div>
              
              <hr className="my-4" />
              
              <p className="text-muted small mb-0">
                If this problem persists, please contact our support team at{' '}
                <a href="mailto:<EMAIL>"><EMAIL></a>
              </p>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default ErrorFallback;
