import { Sequelize, DataTypes } from 'sequelize';
import { sequelize } from '../utils/database.js';
import { logger } from '../utils/logger.js';

// Import model definitions
import UserModel from './User.js';
import TenantModel from './Tenant.js';
import RoleModel from './Role.js';
import PermissionModel from './Permission.js';
import RolePermissionModel from './RolePermission.js';
import UserRoleModel from './UserRole.js';

// Master Data Models
import LicenseEditionModel from './masters/LicenseEdition.js';
import DesignationModel from './masters/Designation.js';
import TallyProductModel from './masters/TallyProduct.js';
import StaffRoleModel from './masters/StaffRole.js';
import ExecutiveModel from './masters/Executive.js';
import IndustryModel from './masters/Industry.js';
import AreaModel from './masters/Area.js';
import NatureOfIssueModel from './masters/NatureOfIssue.js';
import AdditionalServiceModel from './masters/AdditionalService.js';
import CallStatusModel from './masters/CallStatus.js';

// Business Models
import CustomerModel from './Customer.js';
import CustomerContactModel from './CustomerContact.js';
import CustomerTSSModel from './CustomerTSS.js';
import CustomerAMCModel from './CustomerAMC.js';
import ServiceCallModel from './ServiceCall.js';
import ServiceCallItemModel from './ServiceCallItem.js';
import SaleModel from './Sale.js';
import SaleItemModel from './SaleItem.js';
import ReferralModel from './Referral.js';

// Initialize models
const models = {
  // Core models
  User: UserModel(sequelize, DataTypes),
  Tenant: TenantModel(sequelize, DataTypes),
  Role: RoleModel(sequelize, DataTypes),
  Permission: PermissionModel(sequelize, DataTypes),
  RolePermission: RolePermissionModel(sequelize, DataTypes),
  UserRole: UserRoleModel(sequelize, DataTypes),

  // Master data models
  LicenseEdition: LicenseEditionModel(sequelize, DataTypes),
  Designation: DesignationModel(sequelize, DataTypes),
  TallyProduct: TallyProductModel(sequelize, DataTypes),
  StaffRole: StaffRoleModel(sequelize, DataTypes),
  Executive: ExecutiveModel(sequelize, DataTypes),
  Industry: IndustryModel(sequelize, DataTypes),
  Area: AreaModel(sequelize, DataTypes),
  NatureOfIssue: NatureOfIssueModel(sequelize, DataTypes),
  AdditionalService: AdditionalServiceModel(sequelize, DataTypes),
  CallStatus: CallStatusModel(sequelize, DataTypes),

  // Business models
  Customer: CustomerModel(sequelize, DataTypes),
  CustomerContact: CustomerContactModel(sequelize, DataTypes),
  CustomerTSS: CustomerTSSModel(sequelize, DataTypes),
  CustomerAMC: CustomerAMCModel(sequelize, DataTypes),
  ServiceCall: ServiceCallModel(sequelize, DataTypes),
  ServiceCallItem: ServiceCallItemModel(sequelize, DataTypes),
  Sale: SaleModel(sequelize, DataTypes),
  SaleItem: SaleItemModel(sequelize, DataTypes),
  Referral: ReferralModel(sequelize, DataTypes),
};

// Define associations
Object.keys(models).forEach(modelName => {
  if (models[modelName].associate) {
    models[modelName].associate(models);
  }
});

// Add sequelize instance and Sequelize constructor to models object
models.sequelize = sequelize;
models.Sequelize = Sequelize;

// Note: We use migrations instead of sync for database schema management
// Sync is disabled to prevent conflicts with migration system
logger.info('📋 Database models loaded (using migrations for schema management)');

export default models;
export { sequelize, Sequelize };
