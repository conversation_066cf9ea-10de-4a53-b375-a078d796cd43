import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import CustomerList from './CustomerList';
import CustomerForm from './CustomerForm';
import CustomerDetails from './CustomerDetails';

const CustomerRoutes = () => {
  return (
    <Routes>
      <Route index element={<CustomerList />} />
      <Route path="add" element={<CustomerForm />} />
      <Route path=":id" element={<CustomerDetails />} />
      <Route path=":id/edit" element={<CustomerForm />} />
      <Route path="*" element={<Navigate to="/customers" replace />} />
    </Routes>
  );
};

export default CustomerRoutes;
