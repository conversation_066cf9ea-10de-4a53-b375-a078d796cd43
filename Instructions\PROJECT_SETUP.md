# 🚀 SaaS CRM for Tally Resellers - Project Setup Guide

## 📋 Prerequisites

### System Requirements
- **Node.js**: Version 18.x or higher
- **npm**: Version 9.x or higher (or yarn 1.22.x)
- **PostgreSQL**: Version 14.x or higher
- **Git**: Latest version
- **VS Code**: Recommended IDE with extensions

### Required Tools
- **Postman**: For API testing
- **pgAdmin**: PostgreSQL administration tool
- **Chrome DevTools**: For frontend debugging
- **Docker**: Optional, for containerized development

### Development Environment
- **Operating System**: Windows 10/11, macOS 10.15+, or Ubuntu 20.04+
- **RAM**: Minimum 8GB, Recommended 16GB
- **Storage**: Minimum 10GB free space
- **Internet**: Stable connection for package downloads

## 🏗️ Project Structure

```
TallyCRMSaaS/
├── 📁 backend/                         # Backend Node.js application
│   ├── 📁 src/
│   │   ├── 📁 controllers/             # API route controllers
│   │   │   ├── authController.js
│   │   │   ├── customerController.js
│   │   │   ├── masterController.js
│   │   │   ├── serviceController.js
│   │   │   └── salesController.js
│   │   ├── 📁 models/                  # Database models
│   │   │   ├── User.js
│   │   │   ├── Customer.js
│   │   │   ├── ServiceCall.js
│   │   │   └── index.js
│   │   ├── 📁 routes/                  # API routes
│   │   │   ├── auth.js
│   │   │   ├── customers.js
│   │   │   ├── masters.js
│   │   │   ├── services.js
│   │   │   └── sales.js
│   │   ├── 📁 middleware/              # Custom middleware
│   │   │   ├── auth.js
│   │   │   ├── validation.js
│   │   │   ├── multiTenant.js
│   │   │   └── errorHandler.js
│   │   ├── 📁 services/                # Business logic services
│   │   │   ├── authService.js
│   │   │   ├── customerService.js
│   │   │   ├── emailService.js
│   │   │   └── reportService.js
│   │   ├── 📁 utils/                   # Utility functions
│   │   │   ├── database.js
│   │   │   ├── logger.js
│   │   │   ├── validators.js
│   │   │   └── helpers.js
│   │   ├── 📁 config/                  # Configuration files
│   │   │   ├── database.js
│   │   │   ├── jwt.js
│   │   │   └── environment.js
│   │   └── 📄 app.js                   # Main application file
│   ├── 📁 tests/                       # Backend tests
│   │   ├── 📁 unit/
│   │   ├── 📁 integration/
│   │   └── 📁 fixtures/
│   ├── 📁 migrations/                  # Database migrations
│   ├── 📁 seeds/                       # Database seed data
│   ├── 📄 package.json                 # Backend dependencies
│   ├── 📄 .env.example                 # Environment variables template
│   └── 📄 server.js                    # Server entry point
├── 📁 frontend/                        # React frontend application
│   ├── 📁 public/                      # Static assets
│   │   ├── index.html
│   │   ├── favicon.ico
│   │   └── manifest.json
│   ├── 📁 src/
│   │   ├── 📁 components/              # Reusable UI components
│   │   │   ├── 📁 common/              # Common components
│   │   │   │   ├── Header.jsx
│   │   │   │   ├── Sidebar.jsx
│   │   │   │   ├── Footer.jsx
│   │   │   │   └── LoadingSpinner.jsx
│   │   │   ├── 📁 forms/               # Form components
│   │   │   │   ├── CustomerForm.jsx
│   │   │   │   ├── ServiceCallForm.jsx
│   │   │   │   └── SalesForm.jsx
│   │   │   └── 📁 tables/              # Table components
│   │   │       ├── CustomerTable.jsx
│   │   │       ├── ServiceTable.jsx
│   │   │       └── DataTable.jsx
│   │   ├── 📁 pages/                   # Page components
│   │   │   ├── 📁 auth/
│   │   │   │   ├── Login.jsx
│   │   │   │   └── Register.jsx
│   │   │   ├── 📁 dashboard/
│   │   │   │   └── Dashboard.jsx
│   │   │   ├── 📁 customers/
│   │   │   │   ├── CustomerList.jsx
│   │   │   │   ├── CustomerDetail.jsx
│   │   │   │   └── CustomerForm.jsx
│   │   │   ├── 📁 services/
│   │   │   │   ├── ServiceList.jsx
│   │   │   │   └── ServiceForm.jsx
│   │   │   ├── 📁 sales/
│   │   │   │   ├── SalesList.jsx
│   │   │   │   └── SalesForm.jsx
│   │   │   ├── 📁 masters/
│   │   │   │   └── MasterManagement.jsx
│   │   │   └── 📁 reports/
│   │   │       └── Reports.jsx
│   │   ├── 📁 services/                # API service calls
│   │   │   ├── api.js
│   │   │   ├── authService.js
│   │   │   ├── customerService.js
│   │   │   ├── serviceCallService.js
│   │   │   └── salesService.js
│   │   ├── 📁 hooks/                   # Custom React hooks
│   │   │   ├── useAuth.js
│   │   │   ├── useApi.js
│   │   │   └── useLocalStorage.js
│   │   ├── 📁 context/                 # React context providers
│   │   │   ├── AuthContext.js
│   │   │   └── ThemeContext.js
│   │   ├── 📁 utils/                   # Frontend utilities
│   │   │   ├── constants.js
│   │   │   ├── helpers.js
│   │   │   └── validators.js
│   │   ├── 📁 assets/                  # Static assets
│   │   │   ├── 📁 images/
│   │   │   ├── 📁 icons/
│   │   │   └── 📁 styles/
│   │   │       ├── globals.css
│   │   │       └── components.css
│   │   ├── 📄 App.jsx                  # Main App component
│   │   ├── 📄 main.jsx                 # React entry point
│   │   └── 📄 index.css                # Global styles
│   ├── 📁 tests/                       # Frontend tests
│   │   ├── 📁 components/
│   │   ├── 📁 pages/
│   │   └── 📁 utils/
│   ├── 📄 package.json                 # Frontend dependencies
│   ├── 📄 vite.config.js               # Vite configuration
│   ├── 📄 .env.example                 # Environment variables template
│   └── 📄 index.html                   # HTML template
├── 📁 database/                        # Database scripts and schemas
│   ├── 📁 migrations/                  # Database migration files
│   │   ├── 001_initial_schema.sql
│   │   ├── 002_masters_tables.sql
│   │   ├── 003_customers_tables.sql
│   │   └── 004_services_sales_tables.sql
│   ├── 📁 seeds/                       # Seed data files
│   │   ├── 001_default_roles.sql
│   │   ├── 002_master_data.sql
│   │   └── 003_sample_customers.sql
│   ├── 📁 schemas/                     # Database schema documentation
│   │   ├── tables.sql
│   │   ├── indexes.sql
│   │   ├── constraints.sql
│   │   └── rls_policies.sql
│   └── 📄 setup.sql                    # Initial database setup
├── 📁 docs/                           # Project documentation
│   ├── 📁 api/                        # API documentation
│   ├── 📁 user-guides/                # User manuals
│   ├── 📁 technical/                  # Technical documentation
│   └── 📁 deployment/                 # Deployment guides
├── 📁 scripts/                        # Build and deployment scripts
│   ├── 📄 setup.sh                    # Initial project setup
│   ├── 📄 build.sh                    # Build script
│   ├── 📄 deploy.sh                   # Deployment script
│   └── 📄 backup.sh                   # Database backup script
├── 📁 .github/                        # GitHub workflows
│   └── 📁 workflows/
│       ├── ci.yml
│       └── deploy.yml
├── 📄 .gitignore                      # Git ignore rules
├── 📄 README.md                       # Project overview
├── 📄 docker-compose.yml              # Docker configuration
├── 📄 package.json                    # Root package.json
└── 📄 .env.example                    # Environment variables template
```

## 🔧 Initial Setup Commands

### 1. Repository Setup
```bash
# Clone the repository
git clone <repository-url>
cd TallyCRMSaaS

# Create environment files
cp .env.example .env
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env
```

### 2. Backend Setup
```bash
# Navigate to backend directory
cd backend

# Install dependencies
npm install

# Setup database
createdb tallycrm_dev
npm run migrate
npm run seed

# Start development server
npm run dev
```

### 3. Frontend Setup
```bash
# Navigate to frontend directory (in new terminal)
cd frontend

# Install dependencies
npm install

# Start development server
npm run dev
```

### 4. Database Setup
```bash
# Create PostgreSQL database
createdb tallycrm_dev
createdb tallycrm_test

# Run migrations
cd backend
npm run migrate

# Seed initial data
npm run seed
```

## 📦 Package Dependencies

### Backend Dependencies (package.json)
```json
{
  "dependencies": {
    "express": "^4.18.2",
    "pg": "^8.11.0",
    "sequelize": "^6.32.1",
    "jsonwebtoken": "^9.0.1",
    "bcryptjs": "^2.4.3",
    "cors": "^2.8.5",
    "helmet": "^7.0.0",
    "express-rate-limit": "^6.8.1",
    "multer": "^1.4.5",
    "xlsx": "^0.18.5",
    "nodemailer": "^6.9.3",
    "joi": "^17.9.2",
    "winston": "^3.10.0",
    "dotenv": "^16.3.1"
  },
  "devDependencies": {
    "nodemon": "^3.0.1",
    "jest": "^29.6.1",
    "supertest": "^6.3.3",
    "eslint": "^8.44.0"
  }
}
```

### Frontend Dependencies (package.json)
```json
{
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.14.1",
    "axios": "^1.4.0",
    "bootstrap": "^5.3.0",
    "react-bootstrap": "^2.8.0",
    "react-hook-form": "^7.45.1",
    "react-query": "^3.39.3",
    "react-toastify": "^9.1.3",
    "chart.js": "^4.3.0",
    "react-chartjs-2": "^5.2.0",
    "date-fns": "^2.30.0",
    "react-datepicker": "^4.16.0",
    "react-select": "^5.7.4"
  },
  "devDependencies": {
    "@vitejs/plugin-react": "^4.0.3",
    "vite": "^4.4.5",
    "@testing-library/react": "^13.4.0",
    "@testing-library/jest-dom": "^5.17.0",
    "eslint": "^8.44.0",
    "eslint-plugin-react": "^7.32.2"
  }
}
```

## 🎯 Development Scripts

### Backend Scripts
```json
{
  "scripts": {
    "start": "node server.js",
    "dev": "nodemon server.js",
    "test": "jest",
    "test:watch": "jest --watch",
    "migrate": "node scripts/migrate.js",
    "seed": "node scripts/seed.js",
    "lint": "eslint src/",
    "lint:fix": "eslint src/ --fix"
  }
}
```

### Frontend Scripts
```json
{
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview",
    "test": "jest",
    "test:watch": "jest --watch",
    "lint": "eslint src/",
    "lint:fix": "eslint src/ --fix"
  }
}
```

### Root Scripts
```json
{
  "scripts": {
    "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install",
    "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"",
    "dev:backend": "cd backend && npm run dev",
    "dev:frontend": "cd frontend && npm run dev",
    "build": "cd frontend && npm run build",
    "test": "cd backend && npm test && cd ../frontend && npm test",
    "setup": "chmod +x scripts/setup.sh && ./scripts/setup.sh"
  }
}
```

## 🚀 Quick Start

1. **Clone and Setup**:
   ```bash
   git clone <repository-url>
   cd TallyCRMSaaS
   npm run install:all
   ```

2. **Configure Environment**:
   - Copy `.env.example` files and update with your settings
   - Configure database connection strings
   - Set JWT secrets and API keys

3. **Start Development**:
   ```bash
   npm run dev
   ```

4. **Access Application**:
   - Frontend: http://localhost:5173
   - Backend API: http://localhost:3000
   - API Documentation: http://localhost:3000/api-docs

**Next Steps**: Follow the [Development Setup Guide](DEVELOPMENT_SETUP.md) for detailed environment configuration.
