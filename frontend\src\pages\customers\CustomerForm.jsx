import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { FaSave, FaTimes, FaUser, FaBuilding, FaPhone, FaEnvelope, FaMapMarkerAlt } from 'react-icons/fa';

const CustomerForm = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEdit = Boolean(id);
  
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    // Company Information
    companyName: '',
    businessType: '',
    gstNumber: '',
    panNumber: '',
    
    // Contact Person
    contactPerson: '',
    designation: '',
    email: '',
    phone: '',
    alternatePhone: '',
    
    // Address
    address: '',
    city: '',
    state: '',
    pincode: '',
    country: 'India',
    
    // Tally Information
    tallyVersion: '',
    tallySerialNumber: '',
    licenseType: '',
    installationDate: '',
    
    // Business Details
    industry: '',
    annualTurnover: '',
    employeeCount: '',
    
    // Status
    status: 'active',
    notes: ''
  });

  const [errors, setErrors] = useState({});

  // Mock data for edit mode
  useEffect(() => {
    if (isEdit) {
      setLoading(true);
      // Simulate API call
      setTimeout(() => {
        setFormData({
          companyName: 'ABC Enterprises',
          businessType: 'Manufacturing',
          gstNumber: '27**********1ZX',
          panNumber: '**********',
          contactPerson: 'John Doe',
          designation: 'Managing Director',
          email: '<EMAIL>',
          phone: '+91 **********',
          alternatePhone: '+91 **********',
          address: '123 Business Park, Sector 5',
          city: 'Mumbai',
          state: 'Maharashtra',
          pincode: '400001',
          country: 'India',
          tallyVersion: 'Prime',
          tallySerialNumber: 'TP123456789',
          licenseType: 'Multi-User',
          installationDate: '2023-01-15',
          industry: 'Manufacturing',
          annualTurnover: '5-10 Crores',
          employeeCount: '50-100',
          status: 'active',
          notes: 'Important client with multiple locations'
        });
        setLoading(false);
      }, 1000);
    }
  }, [isEdit]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    // Required fields
    if (!formData.companyName.trim()) newErrors.companyName = 'Company name is required';
    if (!formData.contactPerson.trim()) newErrors.contactPerson = 'Contact person is required';
    if (!formData.email.trim()) newErrors.email = 'Email is required';
    if (!formData.phone.trim()) newErrors.phone = 'Phone is required';
    if (!formData.city.trim()) newErrors.city = 'City is required';
    if (!formData.state.trim()) newErrors.state = 'State is required';
    
    // Email validation
    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email';
    }
    
    // Phone validation
    if (formData.phone && !/^\+?[\d\s-()]+$/.test(formData.phone)) {
      newErrors.phone = 'Please enter a valid phone number';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast.error('Please fix the errors in the form');
      return;
    }
    
    setLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      toast.success(isEdit ? 'Customer updated successfully' : 'Customer created successfully');
      navigate('/customers');
    } catch (error) {
      toast.error('Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/customers');
  };

  if (loading && isEdit) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container-fluid">
      {/* Header */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h2 className="mb-0">{isEdit ? 'Edit Customer' : 'Add New Customer'}</h2>
              <p className="text-muted">
                {isEdit ? 'Update customer information' : 'Enter customer details to add to your database'}
              </p>
            </div>
            <div className="d-flex gap-2">
              <button 
                type="button" 
                className="btn btn-outline-secondary"
                onClick={handleCancel}
              >
                <FaTimes className="me-2" />
                Cancel
              </button>
              <button 
                type="submit" 
                form="customerForm"
                className="btn btn-primary"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                    {isEdit ? 'Updating...' : 'Saving...'}
                  </>
                ) : (
                  <>
                    <FaSave className="me-2" />
                    {isEdit ? 'Update Customer' : 'Save Customer'}
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Form */}
      <form id="customerForm" onSubmit={handleSubmit}>
        <div className="row">
          {/* Company Information */}
          <div className="col-lg-6 mb-4">
            <div className="card h-100">
              <div className="card-header">
                <h5 className="card-title mb-0">
                  <FaBuilding className="me-2" />
                  Company Information
                </h5>
              </div>
              <div className="card-body">
                <div className="row">
                  <div className="col-12 mb-3">
                    <label className="form-label">Company Name *</label>
                    <input
                      type="text"
                      className={`form-control ${errors.companyName ? 'is-invalid' : ''}`}
                      name="companyName"
                      value={formData.companyName}
                      onChange={handleInputChange}
                      placeholder="Enter company name"
                    />
                    {errors.companyName && <div className="invalid-feedback">{errors.companyName}</div>}
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Business Type</label>
                    <select
                      className="form-select"
                      name="businessType"
                      value={formData.businessType}
                      onChange={handleInputChange}
                    >
                      <option value="">Select business type</option>
                      <option value="Manufacturing">Manufacturing</option>
                      <option value="Trading">Trading</option>
                      <option value="Service">Service</option>
                      <option value="Retail">Retail</option>
                      <option value="Wholesale">Wholesale</option>
                    </select>
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Industry</label>
                    <select
                      className="form-select"
                      name="industry"
                      value={formData.industry}
                      onChange={handleInputChange}
                    >
                      <option value="">Select industry</option>
                      <option value="Textiles">Textiles</option>
                      <option value="Electronics">Electronics</option>
                      <option value="Automotive">Automotive</option>
                      <option value="Food & Beverages">Food & Beverages</option>
                      <option value="Pharmaceuticals">Pharmaceuticals</option>
                      <option value="IT Services">IT Services</option>
                      <option value="Construction">Construction</option>
                      <option value="Other">Other</option>
                    </select>
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <label className="form-label">GST Number</label>
                    <input
                      type="text"
                      className="form-control"
                      name="gstNumber"
                      value={formData.gstNumber}
                      onChange={handleInputChange}
                      placeholder="27**********1ZX"
                    />
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <label className="form-label">PAN Number</label>
                    <input
                      type="text"
                      className="form-control"
                      name="panNumber"
                      value={formData.panNumber}
                      onChange={handleInputChange}
                      placeholder="**********"
                    />
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Annual Turnover</label>
                    <select
                      className="form-select"
                      name="annualTurnover"
                      value={formData.annualTurnover}
                      onChange={handleInputChange}
                    >
                      <option value="">Select turnover range</option>
                      <option value="Below 1 Crore">Below 1 Crore</option>
                      <option value="1-5 Crores">1-5 Crores</option>
                      <option value="5-10 Crores">5-10 Crores</option>
                      <option value="10-50 Crores">10-50 Crores</option>
                      <option value="Above 50 Crores">Above 50 Crores</option>
                    </select>
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Employee Count</label>
                    <select
                      className="form-select"
                      name="employeeCount"
                      value={formData.employeeCount}
                      onChange={handleInputChange}
                    >
                      <option value="">Select employee count</option>
                      <option value="1-10">1-10</option>
                      <option value="11-50">11-50</option>
                      <option value="51-100">51-100</option>
                      <option value="101-500">101-500</option>
                      <option value="Above 500">Above 500</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="col-lg-6 mb-4">
            <div className="card h-100">
              <div className="card-header">
                <h5 className="card-title mb-0">
                  <FaUser className="me-2" />
                  Contact Information
                </h5>
              </div>
              <div className="card-body">
                <div className="row">
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Contact Person *</label>
                    <input
                      type="text"
                      className={`form-control ${errors.contactPerson ? 'is-invalid' : ''}`}
                      name="contactPerson"
                      value={formData.contactPerson}
                      onChange={handleInputChange}
                      placeholder="Enter contact person name"
                    />
                    {errors.contactPerson && <div className="invalid-feedback">{errors.contactPerson}</div>}
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Designation</label>
                    <input
                      type="text"
                      className="form-control"
                      name="designation"
                      value={formData.designation}
                      onChange={handleInputChange}
                      placeholder="Enter designation"
                    />
                  </div>
                  
                  <div className="col-12 mb-3">
                    <label className="form-label">Email *</label>
                    <input
                      type="email"
                      className={`form-control ${errors.email ? 'is-invalid' : ''}`}
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      placeholder="Enter email address"
                    />
                    {errors.email && <div className="invalid-feedback">{errors.email}</div>}
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Phone *</label>
                    <input
                      type="tel"
                      className={`form-control ${errors.phone ? 'is-invalid' : ''}`}
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      placeholder="+91 **********"
                    />
                    {errors.phone && <div className="invalid-feedback">{errors.phone}</div>}
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Alternate Phone</label>
                    <input
                      type="tel"
                      className="form-control"
                      name="alternatePhone"
                      value={formData.alternatePhone}
                      onChange={handleInputChange}
                      placeholder="+91 **********"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="row">
          {/* Address Information */}
          <div className="col-lg-6 mb-4">
            <div className="card h-100">
              <div className="card-header">
                <h5 className="card-title mb-0">
                  <FaMapMarkerAlt className="me-2" />
                  Address Information
                </h5>
              </div>
              <div className="card-body">
                <div className="row">
                  <div className="col-12 mb-3">
                    <label className="form-label">Address</label>
                    <textarea
                      className="form-control"
                      name="address"
                      value={formData.address}
                      onChange={handleInputChange}
                      rows="3"
                      placeholder="Enter complete address"
                    ></textarea>
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <label className="form-label">City *</label>
                    <input
                      type="text"
                      className={`form-control ${errors.city ? 'is-invalid' : ''}`}
                      name="city"
                      value={formData.city}
                      onChange={handleInputChange}
                      placeholder="Enter city"
                    />
                    {errors.city && <div className="invalid-feedback">{errors.city}</div>}
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <label className="form-label">State *</label>
                    <select
                      className={`form-select ${errors.state ? 'is-invalid' : ''}`}
                      name="state"
                      value={formData.state}
                      onChange={handleInputChange}
                    >
                      <option value="">Select state</option>
                      <option value="Maharashtra">Maharashtra</option>
                      <option value="Delhi">Delhi</option>
                      <option value="Karnataka">Karnataka</option>
                      <option value="Tamil Nadu">Tamil Nadu</option>
                      <option value="Gujarat">Gujarat</option>
                      <option value="Rajasthan">Rajasthan</option>
                      <option value="Uttar Pradesh">Uttar Pradesh</option>
                      <option value="West Bengal">West Bengal</option>
                    </select>
                    {errors.state && <div className="invalid-feedback">{errors.state}</div>}
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Pincode</label>
                    <input
                      type="text"
                      className="form-control"
                      name="pincode"
                      value={formData.pincode}
                      onChange={handleInputChange}
                      placeholder="400001"
                    />
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Country</label>
                    <input
                      type="text"
                      className="form-control"
                      name="country"
                      value={formData.country}
                      onChange={handleInputChange}
                      readOnly
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Tally Information & Status */}
          <div className="col-lg-6 mb-4">
            <div className="card h-100">
              <div className="card-header">
                <h5 className="card-title mb-0">
                  Tally Information & Status
                </h5>
              </div>
              <div className="card-body">
                <div className="row">
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Tally Version</label>
                    <select
                      className="form-select"
                      name="tallyVersion"
                      value={formData.tallyVersion}
                      onChange={handleInputChange}
                    >
                      <option value="">Select version</option>
                      <option value="Prime">Tally Prime</option>
                      <option value="ERP 9">Tally ERP 9</option>
                      <option value="Silver">Tally Silver</option>
                      <option value="Gold">Tally Gold</option>
                    </select>
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <label className="form-label">License Type</label>
                    <select
                      className="form-select"
                      name="licenseType"
                      value={formData.licenseType}
                      onChange={handleInputChange}
                    >
                      <option value="">Select license type</option>
                      <option value="Single User">Single User</option>
                      <option value="Multi-User">Multi-User</option>
                      <option value="Educational">Educational</option>
                    </select>
                  </div>
                  
                  <div className="col-12 mb-3">
                    <label className="form-label">Tally Serial Number</label>
                    <input
                      type="text"
                      className="form-control"
                      name="tallySerialNumber"
                      value={formData.tallySerialNumber}
                      onChange={handleInputChange}
                      placeholder="Enter serial number"
                    />
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Installation Date</label>
                    <input
                      type="date"
                      className="form-control"
                      name="installationDate"
                      value={formData.installationDate}
                      onChange={handleInputChange}
                    />
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Status</label>
                    <select
                      className="form-select"
                      name="status"
                      value={formData.status}
                      onChange={handleInputChange}
                    >
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                      <option value="prospect">Prospect</option>
                    </select>
                  </div>
                  
                  <div className="col-12 mb-3">
                    <label className="form-label">Notes</label>
                    <textarea
                      className="form-control"
                      name="notes"
                      value={formData.notes}
                      onChange={handleInputChange}
                      rows="3"
                      placeholder="Enter any additional notes"
                    ></textarea>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
};

export default CustomerForm;
