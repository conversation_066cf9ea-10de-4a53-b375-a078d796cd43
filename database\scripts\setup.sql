-- TallyCRM Database Setup Script
-- PostgreSQL Database Setup for TallyCRM SaaS Application

-- Create databases
CREATE DATABASE tallycrm_dev;
CREATE DATABASE tallycrm_test;
CREATE DATABASE tallycrm_prod;

-- Create user for the application
CREATE USER tallycrm_user WITH PASSWORD 'tallycrm_password_change_in_production';

-- <PERSON> privileges
GRANT ALL PRIVILEGES ON DATABASE tallycrm_dev TO tallycrm_user;
GRANT ALL PRIVILEGES ON DATABASE tallycrm_test TO tallycrm_user;
GRANT ALL PRIVILEGES ON DATABASE tallycrm_prod TO tallycrm_user;

-- Connect to development database
\c tallycrm_dev;

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create schemas for multi-tenant architecture
CREATE SCHEMA IF NOT EXISTS public;
CREATE SCHEMA IF NOT EXISTS audit;
CREATE SCHEMA IF NOT EXISTS logs;

-- <PERSON> schema permissions
GRANT ALL ON SCHEMA public TO tallycrm_user;
GRANT ALL ON SCHEMA audit TO tallycrm_user;
GRANT ALL ON SCHEMA logs TO tallycrm_user;

-- Connect to test database and repeat setup
\c tallycrm_test;

CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

CREATE SCHEMA IF NOT EXISTS public;
CREATE SCHEMA IF NOT EXISTS audit;
CREATE SCHEMA IF NOT EXISTS logs;

GRANT ALL ON SCHEMA public TO tallycrm_user;
GRANT ALL ON SCHEMA audit TO tallycrm_user;
GRANT ALL ON SCHEMA logs TO tallycrm_user;

-- Connect to production database and repeat setup
\c tallycrm_prod;

CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

CREATE SCHEMA IF NOT EXISTS public;
CREATE SCHEMA IF NOT EXISTS audit;
CREATE SCHEMA IF NOT EXISTS logs;

GRANT ALL ON SCHEMA public TO tallycrm_user;
GRANT ALL ON SCHEMA audit TO tallycrm_user;
GRANT ALL ON SCHEMA logs TO tallycrm_user;
