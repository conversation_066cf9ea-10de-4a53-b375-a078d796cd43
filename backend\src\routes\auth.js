import express from 'express';
import { body } from 'express-validator';
import { catchAsync } from '../middleware/errorHandler.js';
import { validateRequest } from '../middleware/validation.js';
import { authenticate } from '../middleware/auth.js';
import { generateTokenPair, verifyToken } from '../utils/jwt.js';
import { hashPassword, comparePassword, validatePasswordStrength } from '../utils/password.js';
import { logger } from '../utils/logger.js';
import db from '../models/index.js';

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     LoginRequest:
 *       type: object
 *       required:
 *         - email
 *         - password
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *         password:
 *           type: string
 *           minLength: 6
 *     LoginResponse:
 *       type: object
 *       properties:
 *         status:
 *           type: string
 *         token:
 *           type: string
 *         refreshToken:
 *           type: string
 *         user:
 *           type: object
 */

/**
 * @swagger
 * /api/v1/auth/login:
 *   post:
 *     summary: User login
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LoginRequest'
 *     responses:
 *       200:
 *         description: Login successful
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/LoginResponse'
 *       400:
 *         description: Invalid credentials
 *       401:
 *         description: Authentication failed
 */
router.post('/login', [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long'),
  validateRequest,
], catchAsync(async (req, res) => {
  const { email, password } = req.body;

  try {
    // Find user by email
    const user = await db.User.findOne({
      where: { email: email.toLowerCase() },
      include: [{
        model: db.Role,
        as: 'roles',
        include: [{
          model: db.Permission,
          as: 'permissions'
        }]
      }]
    });

    if (!user) {
      logger.warn('Login attempt with non-existent email:', { email, ip: req.ip });
      return res.status(401).json({
        status: 'error',
        message: 'Invalid email or password'
      });
    }

    // Check if user is active
    if (!user.is_active) {
      logger.warn('Login attempt with inactive user:', { email, ip: req.ip });
      return res.status(401).json({
        status: 'error',
        message: 'Account is deactivated. Please contact administrator.'
      });
    }

    // Validate password
    const isValidPassword = await user.validatePassword(password);
    if (!isValidPassword) {
      logger.warn('Login attempt with invalid password:', { email, ip: req.ip });
      return res.status(401).json({
        status: 'error',
        message: 'Invalid email or password'
      });
    }

    // Update last login
    await user.update({ last_login_at: new Date() });

    // Generate tokens
    const { accessToken, refreshToken } = generateTokenPair(user);

    // Prepare user data for response
    const userData = user.toJSON();
    userData.roles = user.roles?.map(role => ({
      id: role.id,
      name: role.name,
      permissions: role.permissions?.map(p => p.name) || []
    })) || [];

    logger.info('User logged in successfully:', {
      userId: user.id,
      email: user.email,
      ip: req.ip
    });

    res.status(200).json({
      status: 'success',
      message: 'Login successful',
      token: accessToken,
      refreshToken: refreshToken,
      user: userData,
    });
  } catch (error) {
    logger.error('Login error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Login failed. Please try again.'
    });
  }
}));

/**
 * @swagger
 * /api/v1/auth/logout:
 *   post:
 *     summary: User logout
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Logout successful
 *       401:
 *         description: Unauthorized
 */
router.post('/logout', authenticate, catchAsync(async (req, res) => {
  try {
    // In a production environment, you might want to:
    // 1. Add the token to a blacklist/revoked tokens list
    // 2. Store revoked tokens in Redis or database
    // 3. Clear any server-side sessions

    logger.info('User logged out:', {
      userId: req.user.id,
      email: req.user.email,
      ip: req.ip
    });

    res.status(200).json({
      status: 'success',
      message: 'Logout successful',
    });
  } catch (error) {
    logger.error('Logout error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Logout failed. Please try again.'
    });
  }
}));

/**
 * @swagger
 * /api/v1/auth/refresh:
 *   post:
 *     summary: Refresh access token
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - refreshToken
 *             properties:
 *               refreshToken:
 *                 type: string
 *     responses:
 *       200:
 *         description: Token refreshed successfully
 *       401:
 *         description: Invalid refresh token
 */
router.post('/refresh', [
  body('refreshToken')
    .notEmpty()
    .withMessage('Refresh token is required'),
  validateRequest,
], catchAsync(async (req, res) => {
  const { refreshToken } = req.body;

  try {
    // Verify refresh token
    const decoded = verifyToken(refreshToken);

    // Find user
    const user = await db.User.findOne({
      where: {
        id: decoded.id,
        is_active: true
      },
      include: [{
        model: db.Role,
        as: 'roles',
        include: [{
          model: db.Permission,
          as: 'permissions'
        }]
      }]
    });

    if (!user) {
      return res.status(401).json({
        status: 'error',
        message: 'Invalid refresh token - user not found'
      });
    }

    // Generate new access token
    const { accessToken } = generateTokenPair(user);

    logger.info('Token refreshed:', {
      userId: user.id,
      email: user.email,
      ip: req.ip
    });

    res.status(200).json({
      status: 'success',
      message: 'Token refreshed successfully',
      token: accessToken,
    });
  } catch (error) {
    logger.error('Token refresh error:', error);
    res.status(401).json({
      status: 'error',
      message: 'Invalid or expired refresh token'
    });
  }
}));

/**
 * @swagger
 * /api/v1/auth/register:
 *   post:
 *     summary: User registration
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - email
 *               - password
 *             properties:
 *               name:
 *                 type: string
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *                 minLength: 6
 *     responses:
 *       201:
 *         description: User registered successfully
 *       400:
 *         description: Invalid input data
 *       409:
 *         description: User already exists
 */
router.post('/register', [
  body('firstName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters'),
  body('lastName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters'),
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('password')
    .custom((value) => {
      const validation = validatePasswordStrength(value);
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '));
      }
      return true;
    }),
  body('tenantId')
    .isUUID()
    .withMessage('Valid tenant ID is required'),
  validateRequest,
], catchAsync(async (req, res) => {
  const { firstName, lastName, email, password, tenantId } = req.body;

  try {
    // Check if user already exists
    const existingUser = await db.User.findOne({
      where: { email: email.toLowerCase() }
    });

    if (existingUser) {
      return res.status(409).json({
        status: 'error',
        message: 'User with this email already exists'
      });
    }

    // Verify tenant exists
    const tenant = await db.Tenant.findByPk(tenantId);
    if (!tenant) {
      return res.status(400).json({
        status: 'error',
        message: 'Invalid tenant ID'
      });
    }

    // Create new user
    const newUser = await db.User.create({
      tenant_id: tenantId,
      email: email.toLowerCase(),
      password: password, // Will be hashed by the model hook
      first_name: firstName,
      last_name: lastName,
      is_active: true,
      is_verified: false
    });

    // Generate tokens
    const { accessToken, refreshToken } = generateTokenPair(newUser);

    // Prepare user data for response
    const userData = newUser.toJSON();

    logger.info('New user registered:', {
      userId: newUser.id,
      email: newUser.email,
      ip: req.ip
    });

    res.status(201).json({
      status: 'success',
      message: 'User registered successfully',
      token: accessToken,
      refreshToken: refreshToken,
      user: userData,
    });
  } catch (error) {
    logger.error('Registration error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Registration failed. Please try again.'
    });
  }
}));

/**
 * @swagger
 * /api/v1/auth/logout:
 *   post:
 *     summary: Logout user
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Logged out successfully
 *       401:
 *         description: Unauthorized
 */
router.post('/logout', authenticate, catchAsync(async (req, res) => {
  try {
    // In a real application, you might want to blacklist the token
    // For now, we'll just return success since JWT tokens are stateless
    logger.info('User logged out', { userId: req.user.id });

    res.json({
      status: 'success',
      message: 'Logged out successfully'
    });
  } catch (error) {
    logger.error('Logout error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Logout failed'
    });
  }
}));

/**
 * @swagger
 * /api/v1/auth/me:
 *   get:
 *     summary: Get current user information
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User information retrieved successfully
 *       401:
 *         description: Unauthorized
 */
router.get('/me', authenticate, catchAsync(async (req, res) => {
  try {
    const user = await db.User.findOne({
      where: { id: req.user.id },
      include: [{
        model: db.Role,
        as: 'roles',
        include: [{
          model: db.Permission,
          as: 'permissions'
        }]
      }, {
        model: db.Tenant,
        as: 'tenant',
        attributes: ['id', 'name', 'domain']
      }]
    });

    if (!user) {
      return res.status(404).json({
        status: 'error',
        message: 'User not found'
      });
    }

    // Prepare user data for response
    const userData = user.toJSON();
    userData.roles = user.roles?.map(role => ({
      id: role.id,
      name: role.name,
      permissions: role.permissions?.map(p => p.name) || []
    })) || [];

    res.status(200).json({
      status: 'success',
      user: userData
    });
  } catch (error) {
    logger.error('Get user info error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to retrieve user information'
    });
  }
}));

/**
 * @swagger
 * /api/v1/auth/change-password:
 *   post:
 *     summary: Change user password
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - currentPassword
 *               - newPassword
 *             properties:
 *               currentPassword:
 *                 type: string
 *               newPassword:
 *                 type: string
 *                 minLength: 8
 *     responses:
 *       200:
 *         description: Password changed successfully
 *       400:
 *         description: Invalid current password or weak new password
 *       401:
 *         description: Unauthorized
 */
router.post('/change-password', [
  authenticate,
  body('currentPassword')
    .notEmpty()
    .withMessage('Current password is required'),
  body('newPassword')
    .custom((value) => {
      const validation = validatePasswordStrength(value);
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '));
      }
      return true;
    }),
  validateRequest,
], catchAsync(async (req, res) => {
  const { currentPassword, newPassword } = req.body;

  try {
    // Find user
    const user = await db.User.findByPk(req.user.id);

    if (!user) {
      return res.status(404).json({
        status: 'error',
        message: 'User not found'
      });
    }

    // Validate current password
    const isCurrentPasswordValid = await user.validatePassword(currentPassword);
    if (!isCurrentPasswordValid) {
      return res.status(400).json({
        status: 'error',
        message: 'Current password is incorrect'
      });
    }

    // Check if new password is different from current
    const isSamePassword = await user.validatePassword(newPassword);
    if (isSamePassword) {
      return res.status(400).json({
        status: 'error',
        message: 'New password must be different from current password'
      });
    }

    // Update password
    await user.update({ password: newPassword });

    logger.info('Password changed:', {
      userId: user.id,
      email: user.email,
      ip: req.ip
    });

    res.status(200).json({
      status: 'success',
      message: 'Password changed successfully'
    });
  } catch (error) {
    logger.error('Password change error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to change password'
    });
  }
}));

export default router;
