# TallyCRM Database Design

## Overview

TallyCRM uses PostgreSQL as the primary database with a multi-tenant architecture designed to support multiple Tally resellers on a single platform while maintaining data isolation and security.

## Database Architecture

### Multi-Tenant Strategy
- **Schema-based Multi-tenancy**: Each tenant (Tally reseller) has their own schema
- **Shared Infrastructure**: Common tables and functions are shared across tenants
- **Data Isolation**: Complete data separation between tenants for security and compliance

### Database Structure

```
tallycrm_dev/
├── public/           # Shared tables and functions
├── tenant_001/       # Tenant-specific schema
├── tenant_002/       # Tenant-specific schema
├── audit/           # Audit logging
└── logs/            # Application logging
```

## Core Entities

### 1. User Management
- **users**: System users with role-based access
- **roles**: User roles and permissions
- **permissions**: Granular permission system
- **user_sessions**: Active user sessions

### 2. Customer Management
- **customers**: Customer master data
- **customer_contacts**: Multiple contacts per customer
- **customer_addresses**: Multiple addresses per customer
- **customer_documents**: Document attachments

### 3. Service Management
- **service_calls**: Service call records
- **service_types**: Types of services offered
- **service_engineers**: Engineer assignments
- **service_schedules**: Scheduling system

### 4. AMC Management
- **amc_contracts**: Annual Maintenance Contracts
- **amc_renewals**: Renewal tracking
- **amc_payments**: Payment records

### 5. Sales Management
- **sales_leads**: Lead management
- **sales_opportunities**: Opportunity tracking
- **sales_orders**: Order management
- **sales_invoices**: Invoice generation

### 6. Master Data
- **products**: Tally products and versions
- **locations**: Geographic locations
- **categories**: Various categorization
- **configurations**: System configurations

## Key Features

### 1. Audit Trail
- Complete audit logging for all data changes
- Automatic trigger-based audit system
- Retention policies for audit data

### 2. Full-Text Search
- PostgreSQL full-text search capabilities
- Search across customers, services, and documents
- Optimized search indexes

### 3. Data Archival
- Automatic archival of old records
- Configurable retention policies
- Performance optimization

### 4. Backup Strategy
- Daily automated backups
- Point-in-time recovery
- Cross-region backup replication

## Performance Optimizations

### Indexing Strategy
- Primary keys on all tables
- Foreign key indexes
- Composite indexes for common queries
- Partial indexes for filtered queries

### Query Optimization
- Materialized views for complex reports
- Query plan analysis and optimization
- Connection pooling

### Partitioning
- Time-based partitioning for large tables
- Automatic partition management
- Partition pruning for performance

## Security Measures

### Access Control
- Row-level security (RLS) for multi-tenancy
- Role-based access control
- Column-level permissions

### Data Encryption
- Encryption at rest
- SSL/TLS for data in transit
- Sensitive data encryption

### Compliance
- GDPR compliance features
- Data retention policies
- Audit trail requirements

## Monitoring and Maintenance

### Health Checks
- Database connection monitoring
- Performance metrics collection
- Automated alerting

### Maintenance Tasks
- Regular VACUUM and ANALYZE
- Index maintenance
- Statistics updates

## Migration Strategy

### Version Control
- Database schema versioning
- Migration scripts
- Rollback procedures

### Deployment
- Blue-green deployments
- Zero-downtime migrations
- Automated testing

## Development Guidelines

### Naming Conventions
- Snake_case for table and column names
- Descriptive names for indexes
- Consistent foreign key naming

### Data Types
- Use appropriate data types
- Avoid VARCHAR without length
- Use TIMESTAMP WITH TIME ZONE

### Constraints
- Primary keys on all tables
- Foreign key constraints
- Check constraints for data validation

## Backup and Recovery

### Backup Schedule
- Full backup: Daily at 2 AM
- Incremental backup: Every 4 hours
- Transaction log backup: Every 15 minutes

### Recovery Procedures
- Point-in-time recovery
- Cross-region restore
- Disaster recovery testing

## Performance Monitoring

### Key Metrics
- Query execution time
- Connection pool usage
- Lock contention
- Index usage statistics

### Alerting
- Slow query alerts
- Connection limit alerts
- Disk space monitoring
- Replication lag monitoring
