import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import SalesList from './SalesList';
import SalesForm from './SalesForm';
import SalesDetails from './SalesDetails';

const SalesRoutes = () => {
  return (
    <Routes>
      <Route index element={<SalesList />} />
      <Route path="add" element={<SalesForm />} />
      <Route path=":id" element={<SalesDetails />} />
      <Route path=":id/edit" element={<SalesForm />} />
      <Route path="*" element={<Navigate to="/sales" replace />} />
    </Routes>
  );
};

export default SalesRoutes;
