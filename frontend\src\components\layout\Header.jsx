import React from 'react';
import { Navbar, Nav, Dropdown } from 'react-bootstrap';

const Header = ({ onToggleSidebar, onToggleSidebarMobile, sidebarCollapsed }) => {
  return (
    <Navbar className="header bg-white border-bottom px-3" expand="lg">
      <div className="d-flex align-items-center w-100">
        {/* Mobile Menu Toggle */}
        <button 
          className="btn btn-link d-md-none me-3 p-0"
          onClick={onToggleSidebarMobile}
        >
          <i className="bi bi-list fs-4"></i>
        </button>
        
        {/* Desktop Menu Toggle */}
        <button 
          className="btn btn-link d-none d-md-block me-3 p-0"
          onClick={onToggleSidebar}
        >
          <i className={`bi ${sidebarCollapsed ? 'bi-chevron-right' : 'bi-chevron-left'} fs-5`}></i>
        </button>
        
        {/* Page Title */}
        <h6 className="mb-0 me-auto">Dashboard</h6>
        
        {/* Right Side Items */}
        <Nav className="ms-auto d-flex align-items-center">
          {/* Notifications */}
          <Dropdown align="end" className="me-3">
            <Dropdown.Toggle 
              variant="link" 
              className="btn-link p-0 border-0 position-relative"
              id="notifications-dropdown"
            >
              <i className="bi bi-bell fs-5 text-muted"></i>
              <span className="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                3
              </span>
            </Dropdown.Toggle>
            
            <Dropdown.Menu style={{ minWidth: '300px' }}>
              <Dropdown.Header>Notifications</Dropdown.Header>
              <Dropdown.Item>
                <div className="d-flex">
                  <div className="flex-shrink-0">
                    <i className="bi bi-info-circle text-info"></i>
                  </div>
                  <div className="flex-grow-1 ms-2">
                    <h6 className="mb-1">New customer registered</h6>
                    <p className="mb-1 small text-muted">John Doe has registered as a new customer</p>
                    <small className="text-muted">2 minutes ago</small>
                  </div>
                </div>
              </Dropdown.Item>
              <Dropdown.Divider />
              <Dropdown.Item className="text-center">
                <small>View all notifications</small>
              </Dropdown.Item>
            </Dropdown.Menu>
          </Dropdown>
          
          {/* User Profile */}
          <Dropdown align="end">
            <Dropdown.Toggle 
              variant="link" 
              className="btn-link p-0 border-0 d-flex align-items-center"
              id="user-dropdown"
            >
              <div className="d-flex align-items-center">
                <div className="me-2 d-none d-sm-block text-end">
                  <div className="fw-semibold">John Doe</div>
                  <small className="text-muted">Administrator</small>
                </div>
                <div className="rounded-circle bg-primary d-flex align-items-center justify-content-center" 
                     style={{ width: '40px', height: '40px' }}>
                  <i className="bi bi-person-fill text-white"></i>
                </div>
              </div>
            </Dropdown.Toggle>
            
            <Dropdown.Menu>
              <Dropdown.Item>
                <i className="bi bi-person me-2"></i>
                Profile
              </Dropdown.Item>
              <Dropdown.Item>
                <i className="bi bi-gear me-2"></i>
                Settings
              </Dropdown.Item>
              <Dropdown.Divider />
              <Dropdown.Item className="text-danger">
                <i className="bi bi-box-arrow-right me-2"></i>
                Logout
              </Dropdown.Item>
            </Dropdown.Menu>
          </Dropdown>
        </Nav>
      </div>
    </Navbar>
  );
};

export default Header;
