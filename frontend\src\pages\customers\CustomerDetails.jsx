import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate, Link } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { 
  FaEdit, 
  FaTrash, 
  FaPhone, 
  FaEnvelope, 
  FaMapMarkerAlt,
  FaBuilding,
  FaUser,
  FaCalendar,
  FaRupeeSign,
  FaTools,
  FaChartLine,
  FaFileInvoice,
  FaPlus
} from 'react-icons/fa';

const CustomerDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [customer, setCustomer] = useState(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  // Mock data - replace with API call
  useEffect(() => {
    const mockCustomer = {
      id: 1,
      name: 'ABC Enterprises',
      contactPerson: '<PERSON>',
      designation: 'Managing Director',
      email: '<EMAIL>',
      phone: '+91 **********',
      alternatePhone: '+91 **********',
      address: '123 Business Park, Sector 5',
      city: 'Mumbai',
      state: 'Maharashtra',
      pincode: '400001',
      country: 'India',
      businessType: 'Manufacturing',
      industry: 'Textiles',
      gstNumber: '27**********1ZX',
      panNumber: '**********',
      annualTurnover: '5-10 Crores',
      employeeCount: '50-100',
      status: 'active',
      tallyVersion: 'Prime',
      tallySerialNumber: 'TP123456789',
      licenseType: 'Multi-User',
      installationDate: '2023-01-15',
      registrationDate: '2023-01-10',
      lastContact: '2024-01-15',
      notes: 'Important client with multiple locations',
      
      // Service history
      services: [
        {
          id: 1,
          type: 'Installation',
          description: 'Tally Prime Installation',
          date: '2023-01-15',
          status: 'completed',
          amount: 15000,
          technician: 'Raj Kumar'
        },
        {
          id: 2,
          type: 'Support',
          description: 'Data backup and recovery',
          date: '2023-06-20',
          status: 'completed',
          amount: 5000,
          technician: 'Priya Sharma'
        },
        {
          id: 3,
          type: 'Training',
          description: 'Advanced Tally features training',
          date: '2023-12-10',
          status: 'pending',
          amount: 8000,
          technician: 'Amit Singh'
        }
      ],
      
      // Payment history
      payments: [
        {
          id: 1,
          date: '2023-01-20',
          amount: 15000,
          method: 'Bank Transfer',
          status: 'received',
          invoiceNumber: 'INV-2023-001'
        },
        {
          id: 2,
          date: '2023-06-25',
          amount: 5000,
          method: 'Cheque',
          status: 'received',
          invoiceNumber: 'INV-2023-045'
        },
        {
          id: 3,
          date: '2023-12-15',
          amount: 8000,
          method: 'UPI',
          status: 'pending',
          invoiceNumber: 'INV-2023-089'
        }
      ],
      
      // Statistics
      stats: {
        totalServices: 12,
        completedServices: 9,
        pendingServices: 3,
        totalRevenue: 85000,
        pendingAmount: 25000,
        lastServiceDate: '2023-12-10'
      }
    };
    
    setTimeout(() => {
      setCustomer(mockCustomer);
      setLoading(false);
    }, 1000);
  }, [id]);

  const handleDelete = () => {
    if (window.confirm('Are you sure you want to delete this customer? This action cannot be undone.')) {
      toast.success('Customer deleted successfully');
      navigate('/customers');
    }
  };

  const getStatusBadge = (status) => {
    const badgeClass = status === 'active' ? 'bg-success' : 
                      status === 'inactive' ? 'bg-secondary' : 'bg-warning';
    return <span className={`badge ${badgeClass}`}>{status.toUpperCase()}</span>;
  };

  const getServiceStatusBadge = (status) => {
    const badgeClass = status === 'completed' ? 'bg-success' : 
                      status === 'pending' ? 'bg-warning' : 
                      status === 'in-progress' ? 'bg-info' : 'bg-danger';
    return <span className={`badge ${badgeClass}`}>{status.toUpperCase()}</span>;
  };

  const getPaymentStatusBadge = (status) => {
    const badgeClass = status === 'received' ? 'bg-success' : 
                      status === 'pending' ? 'bg-warning' : 'bg-danger';
    return <span className={`badge ${badgeClass}`}>{status.toUpperCase()}</span>;
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  if (!customer) {
    return (
      <div className="container-fluid">
        <div className="alert alert-danger" role="alert">
          Customer not found.
        </div>
      </div>
    );
  }

  return (
    <div className="container-fluid">
      {/* Header */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="d-flex justify-content-between align-items-start">
            <div>
              <h2 className="mb-1">{customer.name}</h2>
              <p className="text-muted mb-2">{customer.contactPerson} • {customer.designation}</p>
              <div className="d-flex align-items-center gap-3">
                {getStatusBadge(customer.status)}
                <span className="badge bg-info">{customer.tallyVersion}</span>
                <small className="text-muted">
                  <FaCalendar className="me-1" />
                  Customer since {new Date(customer.registrationDate).toLocaleDateString()}
                </small>
              </div>
            </div>
            <div className="d-flex gap-2">
              <Link to={`/customers/${customer.id}/edit`} className="btn btn-outline-primary">
                <FaEdit className="me-2" />
                Edit
              </Link>
              <button className="btn btn-outline-danger" onClick={handleDelete}>
                <FaTrash className="me-2" />
                Delete
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="row mb-4">
        <div className="col-md-3">
          <div className="card bg-primary text-white">
            <div className="card-body">
              <div className="d-flex justify-content-between">
                <div>
                  <h4 className="mb-0">{customer.stats.totalServices}</h4>
                  <p className="mb-0">Total Services</p>
                </div>
                <FaTools size={24} />
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card bg-success text-white">
            <div className="card-body">
              <div className="d-flex justify-content-between">
                <div>
                  <h4 className="mb-0">₹{customer.stats.totalRevenue.toLocaleString()}</h4>
                  <p className="mb-0">Total Revenue</p>
                </div>
                <FaRupeeSign size={24} />
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card bg-warning text-white">
            <div className="card-body">
              <div className="d-flex justify-content-between">
                <div>
                  <h4 className="mb-0">₹{customer.stats.pendingAmount.toLocaleString()}</h4>
                  <p className="mb-0">Pending Amount</p>
                </div>
                <FaFileInvoice size={24} />
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card bg-info text-white">
            <div className="card-body">
              <div className="d-flex justify-content-between">
                <div>
                  <h4 className="mb-0">{customer.stats.pendingServices}</h4>
                  <p className="mb-0">Pending Services</p>
                </div>
                <FaChartLine size={24} />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="row">
        <div className="col-12">
          <ul className="nav nav-tabs mb-4">
            <li className="nav-item">
              <button 
                className={`nav-link ${activeTab === 'overview' ? 'active' : ''}`}
                onClick={() => setActiveTab('overview')}
              >
                Overview
              </button>
            </li>
            <li className="nav-item">
              <button 
                className={`nav-link ${activeTab === 'services' ? 'active' : ''}`}
                onClick={() => setActiveTab('services')}
              >
                Services ({customer.services.length})
              </button>
            </li>
            <li className="nav-item">
              <button 
                className={`nav-link ${activeTab === 'payments' ? 'active' : ''}`}
                onClick={() => setActiveTab('payments')}
              >
                Payments ({customer.payments.length})
              </button>
            </li>
          </ul>

          {/* Tab Content */}
          {activeTab === 'overview' && (
            <div className="row">
              {/* Company Information */}
              <div className="col-lg-6 mb-4">
                <div className="card h-100">
                  <div className="card-header">
                    <h5 className="card-title mb-0">
                      <FaBuilding className="me-2" />
                      Company Information
                    </h5>
                  </div>
                  <div className="card-body">
                    <div className="row">
                      <div className="col-sm-6 mb-3">
                        <strong>Business Type:</strong>
                        <p className="mb-0">{customer.businessType}</p>
                      </div>
                      <div className="col-sm-6 mb-3">
                        <strong>Industry:</strong>
                        <p className="mb-0">{customer.industry}</p>
                      </div>
                      <div className="col-sm-6 mb-3">
                        <strong>GST Number:</strong>
                        <p className="mb-0">{customer.gstNumber}</p>
                      </div>
                      <div className="col-sm-6 mb-3">
                        <strong>PAN Number:</strong>
                        <p className="mb-0">{customer.panNumber}</p>
                      </div>
                      <div className="col-sm-6 mb-3">
                        <strong>Annual Turnover:</strong>
                        <p className="mb-0">{customer.annualTurnover}</p>
                      </div>
                      <div className="col-sm-6 mb-3">
                        <strong>Employee Count:</strong>
                        <p className="mb-0">{customer.employeeCount}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Contact Information */}
              <div className="col-lg-6 mb-4">
                <div className="card h-100">
                  <div className="card-header">
                    <h5 className="card-title mb-0">
                      <FaUser className="me-2" />
                      Contact Information
                    </h5>
                  </div>
                  <div className="card-body">
                    <div className="mb-3">
                      <div className="d-flex align-items-center mb-2">
                        <FaPhone className="text-muted me-2" />
                        <strong>Primary Phone:</strong>
                      </div>
                      <p className="mb-0 ms-4">{customer.phone}</p>
                    </div>
                    
                    {customer.alternatePhone && (
                      <div className="mb-3">
                        <div className="d-flex align-items-center mb-2">
                          <FaPhone className="text-muted me-2" />
                          <strong>Alternate Phone:</strong>
                        </div>
                        <p className="mb-0 ms-4">{customer.alternatePhone}</p>
                      </div>
                    )}
                    
                    <div className="mb-3">
                      <div className="d-flex align-items-center mb-2">
                        <FaEnvelope className="text-muted me-2" />
                        <strong>Email:</strong>
                      </div>
                      <p className="mb-0 ms-4">{customer.email}</p>
                    </div>
                    
                    <div className="mb-3">
                      <div className="d-flex align-items-center mb-2">
                        <FaMapMarkerAlt className="text-muted me-2" />
                        <strong>Address:</strong>
                      </div>
                      <p className="mb-0 ms-4">
                        {customer.address}<br />
                        {customer.city}, {customer.state} - {customer.pincode}<br />
                        {customer.country}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Tally Information */}
              <div className="col-lg-6 mb-4">
                <div className="card h-100">
                  <div className="card-header">
                    <h5 className="card-title mb-0">
                      Tally Information
                    </h5>
                  </div>
                  <div className="card-body">
                    <div className="row">
                      <div className="col-sm-6 mb-3">
                        <strong>Version:</strong>
                        <p className="mb-0">{customer.tallyVersion}</p>
                      </div>
                      <div className="col-sm-6 mb-3">
                        <strong>License Type:</strong>
                        <p className="mb-0">{customer.licenseType}</p>
                      </div>
                      <div className="col-sm-6 mb-3">
                        <strong>Serial Number:</strong>
                        <p className="mb-0">{customer.tallySerialNumber}</p>
                      </div>
                      <div className="col-sm-6 mb-3">
                        <strong>Installation Date:</strong>
                        <p className="mb-0">{new Date(customer.installationDate).toLocaleDateString()}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Notes */}
              <div className="col-lg-6 mb-4">
                <div className="card h-100">
                  <div className="card-header">
                    <h5 className="card-title mb-0">Notes</h5>
                  </div>
                  <div className="card-body">
                    <p className="mb-0">{customer.notes || 'No notes available'}</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'services' && (
            <div className="row">
              <div className="col-12">
                <div className="card">
                  <div className="card-header d-flex justify-content-between align-items-center">
                    <h5 className="card-title mb-0">Service History</h5>
                    <button className="btn btn-primary btn-sm">
                      <FaPlus className="me-2" />
                      Add Service
                    </button>
                  </div>
                  <div className="card-body">
                    <div className="table-responsive">
                      <table className="table table-hover">
                        <thead className="table-light">
                          <tr>
                            <th>Date</th>
                            <th>Type</th>
                            <th>Description</th>
                            <th>Technician</th>
                            <th>Amount</th>
                            <th>Status</th>
                          </tr>
                        </thead>
                        <tbody>
                          {customer.services.map(service => (
                            <tr key={service.id}>
                              <td>{new Date(service.date).toLocaleDateString()}</td>
                              <td>
                                <span className="badge bg-secondary">{service.type}</span>
                              </td>
                              <td>{service.description}</td>
                              <td>{service.technician}</td>
                              <td>₹{service.amount.toLocaleString()}</td>
                              <td>{getServiceStatusBadge(service.status)}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'payments' && (
            <div className="row">
              <div className="col-12">
                <div className="card">
                  <div className="card-header d-flex justify-content-between align-items-center">
                    <h5 className="card-title mb-0">Payment History</h5>
                    <button className="btn btn-primary btn-sm">
                      <FaPlus className="me-2" />
                      Add Payment
                    </button>
                  </div>
                  <div className="card-body">
                    <div className="table-responsive">
                      <table className="table table-hover">
                        <thead className="table-light">
                          <tr>
                            <th>Date</th>
                            <th>Invoice Number</th>
                            <th>Amount</th>
                            <th>Payment Method</th>
                            <th>Status</th>
                          </tr>
                        </thead>
                        <tbody>
                          {customer.payments.map(payment => (
                            <tr key={payment.id}>
                              <td>{new Date(payment.date).toLocaleDateString()}</td>
                              <td>{payment.invoiceNumber}</td>
                              <td>₹{payment.amount.toLocaleString()}</td>
                              <td>{payment.method}</td>
                              <td>{getPaymentStatusBadge(payment.status)}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CustomerDetails;
