import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { 
  FaPlus, 
  FaEdit, 
  FaEye, 
  FaTrash, 
  FaSearch, 
  FaFilter,
  FaDownload,
  FaRupeeSign,
  FaUser,
  FaCalendar,
  FaChartLine,
  FaTrophy,
  FaHandshake
} from 'react-icons/fa';

const SalesList = () => {
  const navigate = useNavigate();
  const [sales, setSales] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterStage, setFilterStage] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [salesPerPage] = useState(10);

  // Mock data - replace with API call
  useEffect(() => {
    const mockSales = [
      {
        id: 1,
        leadNumber: 'LEAD-2024-001',
        customer: 'ABC Enterprises',
        customerId: 1,
        contactPerson: '<PERSON>e',
        email: '<EMAIL>',
        phone: '+91 **********',
        product: 'Tally Prime',
        productType: 'Software License',
        stage: 'proposal',
        status: 'active',
        priority: 'high',
        source: 'Website',
        assignedTo: 'Rahul Sharma',
        expectedValue: 50000,
        probability: 75,
        expectedCloseDate: '2024-02-15',
        createdDate: '2024-01-10',
        lastActivity: '2024-01-18',
        notes: 'Interested in multi-user license'
      },
      {
        id: 2,
        leadNumber: 'LEAD-2024-002',
        customer: 'XYZ Trading Co.',
        customerId: 2,
        contactPerson: 'Jane Smith',
        email: '<EMAIL>',
        phone: '+91 **********',
        product: 'Tally ERP 9',
        productType: 'Software License',
        stage: 'negotiation',
        status: 'active',
        priority: 'medium',
        source: 'Referral',
        assignedTo: 'Priya Patel',
        expectedValue: 35000,
        probability: 60,
        expectedCloseDate: '2024-02-20',
        createdDate: '2024-01-05',
        lastActivity: '2024-01-17',
        notes: 'Price sensitive customer'
      },
      {
        id: 3,
        leadNumber: 'LEAD-2024-003',
        customer: 'PQR Industries',
        customerId: 3,
        contactPerson: 'Mike Johnson',
        email: '<EMAIL>',
        phone: '+91 **********',
        product: 'Tally Training',
        productType: 'Service',
        stage: 'closed-won',
        status: 'won',
        priority: 'low',
        source: 'Cold Call',
        assignedTo: 'Amit Singh',
        expectedValue: 25000,
        probability: 100,
        expectedCloseDate: '2024-01-15',
        createdDate: '2023-12-20',
        lastActivity: '2024-01-15',
        notes: 'Training completed successfully'
      },
      {
        id: 4,
        leadNumber: 'LEAD-2024-004',
        customer: 'LMN Exports',
        customerId: 4,
        contactPerson: 'Sarah Wilson',
        email: '<EMAIL>',
        phone: '+91 **********',
        product: 'Tally Silver',
        productType: 'Software License',
        stage: 'qualification',
        status: 'active',
        priority: 'medium',
        source: 'Trade Show',
        assignedTo: 'Vikash Gupta',
        expectedValue: 40000,
        probability: 30,
        expectedCloseDate: '2024-03-01',
        createdDate: '2024-01-12',
        lastActivity: '2024-01-16',
        notes: 'Evaluating multiple vendors'
      }
    ];
    
    setTimeout(() => {
      setSales(mockSales);
      setLoading(false);
    }, 1000);
  }, []);

  // Filter sales based on search, status, and stage
  const filteredSales = sales.filter(sale => {
    const matchesSearch = sale.leadNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         sale.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         sale.contactPerson.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         sale.product.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || sale.status === filterStatus;
    const matchesStage = filterStage === 'all' || sale.stage === filterStage;
    return matchesSearch && matchesStatus && matchesStage;
  });

  // Pagination
  const indexOfLastSale = currentPage * salesPerPage;
  const indexOfFirstSale = indexOfLastSale - salesPerPage;
  const currentSales = filteredSales.slice(indexOfFirstSale, indexOfLastSale);
  const totalPages = Math.ceil(filteredSales.length / salesPerPage);

  // Calculate stats
  const totalValue = sales.reduce((sum, sale) => sum + sale.expectedValue, 0);
  const wonDeals = sales.filter(sale => sale.status === 'won');
  const activeDeals = sales.filter(sale => sale.status === 'active');
  const avgDealSize = sales.length > 0 ? totalValue / sales.length : 0;

  const handleDelete = (saleId) => {
    if (window.confirm('Are you sure you want to delete this sales opportunity?')) {
      setSales(sales.filter(sale => sale.id !== saleId));
      toast.success('Sales opportunity deleted successfully');
    }
  };

  const getStatusBadge = (status) => {
    const badgeClass = status === 'won' ? 'bg-success' : 
                      status === 'lost' ? 'bg-danger' : 
                      status === 'active' ? 'bg-primary' : 'bg-secondary';
    return <span className={`badge ${badgeClass}`}>{status.toUpperCase()}</span>;
  };

  const getStageBadge = (stage) => {
    const stageColors = {
      'lead': 'bg-secondary',
      'qualification': 'bg-info',
      'proposal': 'bg-warning',
      'negotiation': 'bg-primary',
      'closed-won': 'bg-success',
      'closed-lost': 'bg-danger'
    };
    const badgeClass = stageColors[stage] || 'bg-secondary';
    return <span className={`badge ${badgeClass}`}>{stage.toUpperCase().replace('-', ' ')}</span>;
  };

  const getPriorityBadge = (priority) => {
    const badgeClass = priority === 'high' ? 'bg-danger' : 
                      priority === 'medium' ? 'bg-warning' : 'bg-success';
    return <span className={`badge ${badgeClass}`}>{priority.toUpperCase()}</span>;
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container-fluid">
      {/* Header */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h2 className="mb-0">Sales Management</h2>
              <p className="text-muted">Track leads, opportunities, and sales pipeline</p>
            </div>
            <div className="d-flex gap-2">
              <button className="btn btn-outline-primary">
                <FaDownload className="me-2" />
                Export
              </button>
              <Link to="/sales/add" className="btn btn-primary">
                <FaPlus className="me-2" />
                New Lead
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="row mb-4">
        <div className="col-md-3">
          <div className="card bg-primary text-white">
            <div className="card-body">
              <div className="d-flex justify-content-between">
                <div>
                  <h4 className="mb-0">{sales.length}</h4>
                  <p className="mb-0">Total Opportunities</p>
                </div>
                <FaChartLine size={24} />
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card bg-success text-white">
            <div className="card-body">
              <div className="d-flex justify-content-between">
                <div>
                  <h4 className="mb-0">{wonDeals.length}</h4>
                  <p className="mb-0">Won Deals</p>
                </div>
                <FaTrophy size={24} />
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card bg-info text-white">
            <div className="card-body">
              <div className="d-flex justify-content-between">
                <div>
                  <h4 className="mb-0">₹{totalValue.toLocaleString()}</h4>
                  <p className="mb-0">Pipeline Value</p>
                </div>
                <FaRupeeSign size={24} />
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card bg-warning text-white">
            <div className="card-body">
              <div className="d-flex justify-content-between">
                <div>
                  <h4 className="mb-0">₹{Math.round(avgDealSize).toLocaleString()}</h4>
                  <p className="mb-0">Avg Deal Size</p>
                </div>
                <FaHandshake size={24} />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="row mb-4">
        <div className="col-md-4">
          <div className="input-group">
            <span className="input-group-text">
              <FaSearch />
            </span>
            <input
              type="text"
              className="form-control"
              placeholder="Search opportunities..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
        <div className="col-md-2">
          <select
            className="form-select"
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="won">Won</option>
            <option value="lost">Lost</option>
          </select>
        </div>
        <div className="col-md-2">
          <select
            className="form-select"
            value={filterStage}
            onChange={(e) => setFilterStage(e.target.value)}
          >
            <option value="all">All Stages</option>
            <option value="lead">Lead</option>
            <option value="qualification">Qualification</option>
            <option value="proposal">Proposal</option>
            <option value="negotiation">Negotiation</option>
            <option value="closed-won">Closed Won</option>
            <option value="closed-lost">Closed Lost</option>
          </select>
        </div>
        <div className="col-md-4">
          <button className="btn btn-outline-secondary w-100">
            <FaFilter className="me-2" />
            More Filters
          </button>
        </div>
      </div>

      {/* Sales Table */}
      <div className="row">
        <div className="col-12">
          <div className="card">
            <div className="card-body">
              <div className="table-responsive">
                <table className="table table-hover">
                  <thead className="table-light">
                    <tr>
                      <th>Lead #</th>
                      <th>Customer</th>
                      <th>Product</th>
                      <th>Stage</th>
                      <th>Status</th>
                      <th>Priority</th>
                      <th>Assigned To</th>
                      <th>Value</th>
                      <th>Probability</th>
                      <th>Close Date</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {currentSales.map(sale => (
                      <tr key={sale.id}>
                        <td>
                          <strong>{sale.leadNumber}</strong>
                        </td>
                        <td>
                          <div>
                            <h6 className="mb-0">{sale.customer}</h6>
                            <small className="text-muted">{sale.contactPerson}</small>
                          </div>
                        </td>
                        <td>
                          <div>
                            <span className="fw-bold">{sale.product}</span>
                            <br />
                            <small className="text-muted">{sale.productType}</small>
                          </div>
                        </td>
                        <td>{getStageBadge(sale.stage)}</td>
                        <td>{getStatusBadge(sale.status)}</td>
                        <td>{getPriorityBadge(sale.priority)}</td>
                        <td>{sale.assignedTo}</td>
                        <td>
                          <div className="d-flex align-items-center">
                            <FaRupeeSign className="text-muted me-1" size={12} />
                            <strong>{sale.expectedValue.toLocaleString()}</strong>
                          </div>
                        </td>
                        <td>
                          <div className="d-flex align-items-center">
                            <div className="progress me-2" style={{ width: '60px', height: '8px' }}>
                              <div 
                                className="progress-bar bg-success" 
                                style={{ width: `${sale.probability}%` }}
                              ></div>
                            </div>
                            <small>{sale.probability}%</small>
                          </div>
                        </td>
                        <td>
                          <div className="d-flex align-items-center">
                            <FaCalendar className="text-muted me-2" size={12} />
                            {new Date(sale.expectedCloseDate).toLocaleDateString()}
                          </div>
                        </td>
                        <td>
                          <div className="btn-group" role="group">
                            <button
                              className="btn btn-sm btn-outline-primary"
                              onClick={() => navigate(`/sales/${sale.id}`)}
                              title="View Details"
                            >
                              <FaEye />
                            </button>
                            <button
                              className="btn btn-sm btn-outline-secondary"
                              onClick={() => navigate(`/sales/${sale.id}/edit`)}
                              title="Edit"
                            >
                              <FaEdit />
                            </button>
                            <button
                              className="btn btn-sm btn-outline-danger"
                              onClick={() => handleDelete(sale.id)}
                              title="Delete"
                            >
                              <FaTrash />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <nav className="mt-4">
                  <ul className="pagination justify-content-center">
                    <li className={`page-item ${currentPage === 1 ? 'disabled' : ''}`}>
                      <button
                        className="page-link"
                        onClick={() => setCurrentPage(currentPage - 1)}
                        disabled={currentPage === 1}
                      >
                        Previous
                      </button>
                    </li>
                    {[...Array(totalPages)].map((_, index) => (
                      <li key={index} className={`page-item ${currentPage === index + 1 ? 'active' : ''}`}>
                        <button
                          className="page-link"
                          onClick={() => setCurrentPage(index + 1)}
                        >
                          {index + 1}
                        </button>
                      </li>
                    ))}
                    <li className={`page-item ${currentPage === totalPages ? 'disabled' : ''}`}>
                      <button
                        className="page-link"
                        onClick={() => setCurrentPage(currentPage + 1)}
                        disabled={currentPage === totalPages}
                      >
                        Next
                      </button>
                    </li>
                  </ul>
                </nav>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SalesList;
