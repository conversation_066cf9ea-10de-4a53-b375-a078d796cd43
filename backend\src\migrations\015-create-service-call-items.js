import { DataTypes } from 'sequelize';

export const up = async (queryInterface) => {
  await queryInterface.createTable('service_call_items', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    service_call_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'service_calls',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    item_type: {
      type: DataTypes.ENUM('product', 'service', 'additional_service'),
      allowNull: false,
      defaultValue: 'service',
    },
    product_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'tally_products',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    additional_service_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'additional_services',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    description: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    quantity: {
      type: DataTypes.DECIMAL(10, 3),
      allowNull: false,
      defaultValue: 1.000,
    },
    unit: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'Nos',
    },
    rate: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
    },
    amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
    },
    discount_percentage: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    discount_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    taxable_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
    },
    gst_rate: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: false,
      defaultValue: 18.00,
    },
    cgst_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    sgst_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    igst_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    total_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
    },
    is_billable: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    is_completed: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    completion_notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  });

  // Add indexes
  await queryInterface.addIndex('service_call_items', ['service_call_id']);
  await queryInterface.addIndex('service_call_items', ['item_type']);
  await queryInterface.addIndex('service_call_items', ['product_id']);
  await queryInterface.addIndex('service_call_items', ['additional_service_id']);
  await queryInterface.addIndex('service_call_items', ['is_billable']);
  await queryInterface.addIndex('service_call_items', ['is_completed']);
};

export const down = async (queryInterface) => {
  await queryInterface.dropTable('service_call_items');
};
