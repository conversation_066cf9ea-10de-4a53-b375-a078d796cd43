import React from 'react';
import { NavLink } from 'react-router-dom';

const Sidebar = ({ collapsed, visible, onToggle, onClose }) => {
  const menuItems = [
    { path: '/dashboard', icon: 'bi-speedometer2', label: 'Dashboard' },
    { path: '/customers', icon: 'bi-people', label: 'Customers' },
    { path: '/services', icon: 'bi-tools', label: 'Services' },
    { path: '/sales', icon: 'bi-graph-up', label: 'Sales' },
    { path: '/masters', icon: 'bi-gear', label: 'Masters' },
    { path: '/reports', icon: 'bi-bar-chart', label: 'Reports' },
    { path: '/settings', icon: 'bi-sliders', label: 'Settings' },
  ];

  return (
    <>
      {/* Mobile Overlay */}
      {visible && (
        <div 
          className="position-fixed w-100 h-100 bg-dark bg-opacity-50 d-md-none"
          style={{ zIndex: 1040 }}
          onClick={onClose}
        />
      )}
      
      <div className={`sidebar ${collapsed ? 'collapsed' : ''} ${visible ? 'show' : ''}`}>
        <div className="p-3 border-bottom border-secondary">
          <div className="d-flex align-items-center">
            {!collapsed && (
              <>
                <h5 className="text-white mb-0 me-auto">TallyCRM</h5>
                <button 
                  className="btn btn-link text-white p-0 d-none d-md-block"
                  onClick={onToggle}
                >
                  <i className="bi bi-list"></i>
                </button>
              </>
            )}
            {collapsed && (
              <button 
                className="btn btn-link text-white p-0 mx-auto d-none d-md-block"
                onClick={onToggle}
              >
                <i className="bi bi-list"></i>
              </button>
            )}
            <button 
              className="btn btn-link text-white p-0 ms-auto d-md-none"
              onClick={onClose}
            >
              <i className="bi bi-x-lg"></i>
            </button>
          </div>
        </div>
        
        <nav className="flex-grow-1 py-3">
          {menuItems.map((item) => (
            <NavLink
              key={item.path}
              to={item.path}
              className={({ isActive }) => 
                `nav-link ${isActive ? 'active' : ''}`
              }
              onClick={() => window.innerWidth < 768 && onClose()}
            >
              <i className={`bi ${item.icon}`}></i>
              <span>{item.label}</span>
            </NavLink>
          ))}
        </nav>
        
        <div className="p-3 border-top border-secondary">
          <div className="nav-link">
            <i className="bi bi-person-circle"></i>
            <span>Profile</span>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
