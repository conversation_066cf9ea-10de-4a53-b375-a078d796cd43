import multer from 'multer';
import path from 'path';
import fs from 'fs/promises';
import { v4 as uuidv4 } from 'uuid';
import { logger } from './logger.js';

/**
 * File upload utility functions
 */

// Allowed file types
const ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
const ALLOWED_DOCUMENT_TYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'text/plain',
  'text/csv',
];

// File size limits (in bytes)
const MAX_IMAGE_SIZE = 5 * 1024 * 1024; // 5MB
const MAX_DOCUMENT_SIZE = 10 * 1024 * 1024; // 10MB

/**
 * Create upload directory if it doesn't exist
 * @param {string} dirPath - Directory path
 */
const ensureUploadDir = async (dirPath) => {
  try {
    await fs.access(dirPath);
  } catch {
    await fs.mkdir(dirPath, { recursive: true });
    logger.info('Created upload directory:', dirPath);
  }
};

/**
 * Generate unique filename
 * @param {string} originalName - Original filename
 * @returns {string} Unique filename
 */
const generateUniqueFilename = (originalName) => {
  const ext = path.extname(originalName);
  const name = path.basename(originalName, ext);
  const timestamp = Date.now();
  const uuid = uuidv4().split('-')[0];
  return `${name}_${timestamp}_${uuid}${ext}`;
};

/**
 * File filter function
 * @param {Array} allowedTypes - Allowed MIME types
 * @returns {Function} Multer file filter function
 */
const createFileFilter = (allowedTypes) => {
  return (req, file, cb) => {
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error(`Invalid file type. Allowed types: ${allowedTypes.join(', ')}`), false);
    }
  };
};

/**
 * Create multer storage configuration
 * @param {string} uploadPath - Upload directory path
 * @returns {Object} Multer storage configuration
 */
const createStorage = (uploadPath) => {
  return multer.diskStorage({
    destination: async (req, file, cb) => {
      try {
        await ensureUploadDir(uploadPath);
        cb(null, uploadPath);
      } catch (error) {
        cb(error);
      }
    },
    filename: (req, file, cb) => {
      const uniqueName = generateUniqueFilename(file.originalname);
      cb(null, uniqueName);
    },
  });
};

/**
 * Image upload middleware
 */
export const uploadImage = multer({
  storage: createStorage(path.join(process.cwd(), 'uploads', 'images')),
  fileFilter: createFileFilter(ALLOWED_IMAGE_TYPES),
  limits: {
    fileSize: MAX_IMAGE_SIZE,
    files: 5, // Maximum 5 files
  },
});

/**
 * Document upload middleware
 */
export const uploadDocument = multer({
  storage: createStorage(path.join(process.cwd(), 'uploads', 'documents')),
  fileFilter: createFileFilter(ALLOWED_DOCUMENT_TYPES),
  limits: {
    fileSize: MAX_DOCUMENT_SIZE,
    files: 10, // Maximum 10 files
  },
});

/**
 * Profile image upload middleware (single file)
 */
export const uploadProfileImage = multer({
  storage: createStorage(path.join(process.cwd(), 'uploads', 'profiles')),
  fileFilter: createFileFilter(ALLOWED_IMAGE_TYPES),
  limits: {
    fileSize: MAX_IMAGE_SIZE,
    files: 1,
  },
});

/**
 * Service call attachment upload middleware
 */
export const uploadServiceCallAttachment = multer({
  storage: createStorage(path.join(process.cwd(), 'uploads', 'service-calls')),
  fileFilter: createFileFilter([...ALLOWED_IMAGE_TYPES, ...ALLOWED_DOCUMENT_TYPES]),
  limits: {
    fileSize: MAX_DOCUMENT_SIZE,
    files: 10,
  },
});

/**
 * Delete file from filesystem
 * @param {string} filePath - File path to delete
 * @returns {Promise<boolean>} True if file was deleted
 */
export const deleteFile = async (filePath) => {
  try {
    await fs.unlink(filePath);
    logger.info('File deleted:', filePath);
    return true;
  } catch (error) {
    if (error.code !== 'ENOENT') {
      logger.error('Error deleting file:', error);
    }
    return false;
  }
};

/**
 * Delete multiple files
 * @param {Array<string>} filePaths - Array of file paths to delete
 * @returns {Promise<Array>} Array of deletion results
 */
export const deleteFiles = async (filePaths) => {
  const results = [];
  
  for (const filePath of filePaths) {
    const deleted = await deleteFile(filePath);
    results.push({ filePath, deleted });
  }
  
  return results;
};

/**
 * Get file information
 * @param {string} filePath - File path
 * @returns {Promise<Object>} File information
 */
export const getFileInfo = async (filePath) => {
  try {
    const stats = await fs.stat(filePath);
    const ext = path.extname(filePath);
    const name = path.basename(filePath, ext);
    
    return {
      name,
      extension: ext,
      size: stats.size,
      created: stats.birthtime,
      modified: stats.mtime,
      isFile: stats.isFile(),
      isDirectory: stats.isDirectory(),
    };
  } catch (error) {
    logger.error('Error getting file info:', error);
    return null;
  }
};

/**
 * Validate file size
 * @param {number} size - File size in bytes
 * @param {number} maxSize - Maximum allowed size in bytes
 * @returns {boolean} True if file size is valid
 */
export const validateFileSize = (size, maxSize) => {
  return size <= maxSize;
};

/**
 * Validate file type
 * @param {string} mimetype - File MIME type
 * @param {Array<string>} allowedTypes - Allowed MIME types
 * @returns {boolean} True if file type is valid
 */
export const validateFileType = (mimetype, allowedTypes) => {
  return allowedTypes.includes(mimetype);
};

/**
 * Format file size for display
 * @param {number} bytes - File size in bytes
 * @returns {string} Formatted file size
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Get file extension from filename
 * @param {string} filename - Filename
 * @returns {string} File extension
 */
export const getFileExtension = (filename) => {
  return path.extname(filename).toLowerCase();
};

/**
 * Check if file is an image
 * @param {string} mimetype - File MIME type
 * @returns {boolean} True if file is an image
 */
export const isImage = (mimetype) => {
  return ALLOWED_IMAGE_TYPES.includes(mimetype);
};

/**
 * Check if file is a document
 * @param {string} mimetype - File MIME type
 * @returns {boolean} True if file is a document
 */
export const isDocument = (mimetype) => {
  return ALLOWED_DOCUMENT_TYPES.includes(mimetype);
};

/**
 * Create file upload response
 * @param {Object} file - Multer file object
 * @param {string} baseUrl - Base URL for file access
 * @returns {Object} File upload response
 */
export const createFileResponse = (file, baseUrl = '') => {
  return {
    filename: file.filename,
    originalName: file.originalname,
    mimetype: file.mimetype,
    size: file.size,
    path: file.path,
    url: `${baseUrl}/uploads/${path.relative(path.join(process.cwd(), 'uploads'), file.path)}`,
    uploadedAt: new Date(),
  };
};

/**
 * Handle multer errors
 * @param {Error} error - Multer error
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
export const handleUploadError = (error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    let message = 'File upload error';
    
    switch (error.code) {
      case 'LIMIT_FILE_SIZE':
        message = 'File size too large';
        break;
      case 'LIMIT_FILE_COUNT':
        message = 'Too many files';
        break;
      case 'LIMIT_UNEXPECTED_FILE':
        message = 'Unexpected file field';
        break;
      case 'LIMIT_PART_COUNT':
        message = 'Too many parts';
        break;
      case 'LIMIT_FIELD_KEY':
        message = 'Field name too long';
        break;
      case 'LIMIT_FIELD_VALUE':
        message = 'Field value too long';
        break;
      case 'LIMIT_FIELD_COUNT':
        message = 'Too many fields';
        break;
    }
    
    logger.error('Multer upload error:', error);
    
    return res.status(400).json({
      success: false,
      message,
      error: error.message,
    });
  }
  
  if (error.message.includes('Invalid file type')) {
    return res.status(400).json({
      success: false,
      message: 'Invalid file type',
      error: error.message,
    });
  }
  
  logger.error('Upload error:', error);
  
  res.status(500).json({
    success: false,
    message: 'File upload failed',
    error: process.env.NODE_ENV === 'development' ? error.message : undefined,
  });
};

/**
 * Clean up old files (for maintenance)
 * @param {string} directory - Directory to clean
 * @param {number} maxAge - Maximum age in days
 * @returns {Promise<number>} Number of files deleted
 */
export const cleanupOldFiles = async (directory, maxAge = 30) => {
  try {
    const files = await fs.readdir(directory);
    const cutoffDate = new Date(Date.now() - maxAge * 24 * 60 * 60 * 1000);
    let deletedCount = 0;
    
    for (const file of files) {
      const filePath = path.join(directory, file);
      const stats = await fs.stat(filePath);
      
      if (stats.birthtime < cutoffDate) {
        await deleteFile(filePath);
        deletedCount++;
      }
    }
    
    logger.info(`Cleaned up ${deletedCount} old files from ${directory}`);
    return deletedCount;
  } catch (error) {
    logger.error('Error cleaning up old files:', error);
    return 0;
  }
};
