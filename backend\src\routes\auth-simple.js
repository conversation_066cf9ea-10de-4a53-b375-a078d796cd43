import express from 'express';
import { body } from 'express-validator';
import bcrypt from 'bcryptjs';
import { Sequelize } from 'sequelize';
import { generateTokenPair, verifyToken } from '../utils/jwt.js';
import { validateRequest } from '../middleware/validation.js';

const router = express.Router();

// Database connection
const sequelize = new Sequelize({
  dialect: 'sqlite',
  storage: './database/tallycrm_dev.sqlite',
  logging: false,
});

/**
 * Login endpoint
 */
router.post('/login', [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long'),
  validateRequest,
], async (req, res) => {
  try {
    const { email, password } = req.body;
    
    // Find user
    const [users] = await sequelize.query(`
      SELECT u.*, t.name as tenant_name, t.subdomain as tenant_subdomain
      FROM users u
      JOIN tenants t ON u.tenant_id = t.id
      WHERE u.email = ? AND u.is_active = 1
    `, {
      replacements: [email.toLowerCase()],
      type: Sequelize.QueryTypes.SELECT
    });

    if (!users) {
      return res.status(401).json({
        status: 'error',
        message: 'Invalid email or password'
      });
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, users.password);
    if (!isValidPassword) {
      return res.status(401).json({
        status: 'error',
        message: 'Invalid email or password'
      });
    }

    // Get user roles and permissions
    const [userRoles] = await sequelize.query(`
      SELECT r.name as role_name, p.name as permission_name
      FROM user_roles ur
      JOIN roles r ON ur.role_id = r.id
      LEFT JOIN role_permissions rp ON r.id = rp.role_id
      LEFT JOIN permissions p ON rp.permission_id = p.id
      WHERE ur.user_id = ?
    `, {
      replacements: [users.id],
      type: Sequelize.QueryTypes.SELECT
    });

    // Organize roles and permissions
    const roles = {};
    userRoles.forEach(row => {
      if (!roles[row.role_name]) {
        roles[row.role_name] = {
          name: row.role_name,
          permissions: []
        };
      }
      if (row.permission_name) {
        roles[row.role_name].permissions.push(row.permission_name);
      }
    });

    // Update last login
    await sequelize.query(`
      UPDATE users SET last_login_at = CURRENT_TIMESTAMP WHERE id = ?
    `, {
      replacements: [users.id]
    });

    // Generate tokens
    const tokenPayload = {
      id: users.id,
      email: users.email,
      tenantId: users.tenant_id,
      roles: Object.keys(roles),
      permissions: Object.values(roles).flatMap(r => r.permissions)
    };
    
    const { accessToken, refreshToken } = generateTokenPair(tokenPayload);

    // Prepare response
    const userData = {
      id: users.id,
      email: users.email,
      firstName: users.first_name,
      lastName: users.last_name,
      phone: users.phone,
      isActive: users.is_active,
      isVerified: users.is_verified,
      tenant: {
        id: users.tenant_id,
        name: users.tenant_name,
        subdomain: users.tenant_subdomain
      },
      roles: Object.values(roles),
      permissions: Object.values(roles).flatMap(r => r.permissions)
    };

    res.status(200).json({
      status: 'success',
      message: 'Login successful',
      token: accessToken,
      refreshToken: refreshToken,
      user: userData,
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Login failed. Please try again.'
    });
  }
});

/**
 * Get user profile
 */
router.get('/profile', async (req, res) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    if (!token) {
      return res.status(401).json({
        status: 'error',
        message: 'Access token required'
      });
    }

    const decoded = verifyToken(token);
    if (!decoded) {
      return res.status(401).json({
        status: 'error',
        message: 'Invalid token'
      });
    }

    // Get user data
    const [users] = await sequelize.query(`
      SELECT u.*, t.name as tenant_name, t.subdomain as tenant_subdomain
      FROM users u
      JOIN tenants t ON u.tenant_id = t.id
      WHERE u.id = ? AND u.is_active = 1
    `, {
      replacements: [decoded.id],
      type: Sequelize.QueryTypes.SELECT
    });

    if (!users) {
      return res.status(404).json({
        status: 'error',
        message: 'User not found'
      });
    }

    const userData = {
      id: users.id,
      email: users.email,
      firstName: users.first_name,
      lastName: users.last_name,
      phone: users.phone,
      isActive: users.is_active,
      isVerified: users.is_verified,
      tenant: {
        id: users.tenant_id,
        name: users.tenant_name,
        subdomain: users.tenant_subdomain
      }
    };

    res.status(200).json({
      status: 'success',
      user: userData
    });

  } catch (error) {
    console.error('Profile error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get profile'
    });
  }
});

/**
 * Logout endpoint
 */
router.post('/logout', (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'Logout successful'
  });
});

export default router;
