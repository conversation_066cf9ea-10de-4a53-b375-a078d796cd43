/* TallyCRM App Specific Styles */

.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Layout Styles */
.main-layout {
  display: flex;
  min-height: 100vh;
}

.sidebar {
  width: var(--sidebar-width);
  background-color: var(--brand-dark);
  color: white;
  transition: width var(--animation-normal) ease;
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: var(--z-fixed);
  overflow-y: auto;
}

.sidebar.collapsed {
  width: var(--sidebar-collapsed-width);
}

.main-content {
  flex: 1;
  margin-left: var(--sidebar-width);
  transition: margin-left var(--animation-normal) ease;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content.sidebar-collapsed {
  margin-left: var(--sidebar-collapsed-width);
}

.header {
  height: var(--header-height);
  background-color: white;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  padding: 0 1.5rem;
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

.content-area {
  flex: 1;
  padding: 1.5rem;
  background-color: var(--bg-primary);
}

.footer {
  height: var(--footer-height);
  background-color: white;
  border-top: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 1.5rem;
  margin-top: auto;
}

/* Auth Layout */
.auth-layout {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--brand-primary) 0%, var(--tally-blue) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.auth-card {
  width: 100%;
  max-width: 400px;
  background: white;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  padding: 2rem;
}

.auth-logo {
  text-align: center;
  margin-bottom: 2rem;
}

.auth-logo h1 {
  color: var(--brand-primary);
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.auth-logo p {
  color: var(--muted);
  margin-bottom: 0;
}

/* Navigation Styles */
.nav-link {
  color: rgba(255, 255, 255, 0.8);
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  text-decoration: none;
  transition: var(--transition);
  border-radius: var(--border-radius);
  margin: 0.25rem 0.5rem;
}

.nav-link:hover {
  color: white;
  background-color: rgba(255, 255, 255, 0.1);
  text-decoration: none;
}

.nav-link.active {
  color: white;
  background-color: var(--brand-primary);
}

.nav-link i {
  margin-right: 0.75rem;
  width: 1.25rem;
  text-align: center;
}

.sidebar.collapsed .nav-link span {
  display: none;
}

.sidebar.collapsed .nav-link {
  justify-content: center;
}

.sidebar.collapsed .nav-link i {
  margin-right: 0;
}

/* Dashboard Styles */
.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
  border-left: 4px solid var(--brand-primary);
  transition: var(--transition);
}

.stat-card:hover {
  box-shadow: var(--shadow);
  transform: translateY(-2px);
}

.stat-card .stat-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.stat-card .stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--brand-dark);
  margin-bottom: 0.5rem;
}

.stat-card .stat-label {
  color: var(--muted);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Table Styles */
.table-responsive {
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-sm);
  background: white;
}

.table th {
  background-color: var(--bg-secondary);
  border-bottom: 2px solid var(--border-color);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.875rem;
  letter-spacing: 0.5px;
}

/* Form Styles */
.form-floating > label {
  color: var(--muted);
}

.form-control:focus + label {
  color: var(--brand-primary);
}

/* Button Styles */
.btn-primary {
  background-color: var(--brand-primary);
  border-color: var(--brand-primary);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

.btn-tally {
  background-color: var(--tally-red);
  border-color: var(--tally-red);
  color: white;
}

.btn-tally:hover {
  background-color: #c41e24;
  border-color: #c41e24;
  color: white;
}

/* Loading States */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
}

.loading-spinner {
  width: 3rem;
  height: 3rem;
}

/* Error States */
.error-boundary {
  padding: 2rem;
  text-align: center;
  background-color: var(--bg-light);
  border-radius: var(--border-radius);
  margin: 2rem;
}

.error-boundary h2 {
  color: var(--brand-danger);
  margin-bottom: 1rem;
}

.error-boundary p {
  color: var(--muted);
  margin-bottom: 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform var(--animation-normal) ease;
  }
  
  .sidebar.show {
    transform: translateX(0);
  }
  
  .main-content {
    margin-left: 0;
  }
  
  .dashboard-stats {
    grid-template-columns: 1fr;
  }
  
  .auth-card {
    margin: 1rem;
    padding: 1.5rem;
  }
}

@media (max-width: 576px) {
  .content-area {
    padding: 1rem;
  }
  
  .header {
    padding: 0 1rem;
  }
}
