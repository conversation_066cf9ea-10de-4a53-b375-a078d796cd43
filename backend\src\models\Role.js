import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const Role = sequelize.define('Role', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [2, 50],
      },
    },
    slug: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        is: /^[a-zA-Z0-9_]+$/,
        len: [2, 50],
      },
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    is_system: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'System roles cannot be deleted',
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    level: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
      comment: 'Role hierarchy level (1=highest)',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'roles',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['slug'],
        unique: true,
      },
      {
        fields: ['is_active'],
      },
      {
        fields: ['level'],
      },
    ],
  });

  // Class methods
  Role.getSystemRoles = function() {
    return [
      {
        name: 'Super Admin',
        slug: 'super_admin',
        description: 'Full system access',
        is_system: true,
        level: 1,
      },
      {
        name: 'Admin',
        slug: 'admin',
        description: 'Administrative access',
        is_system: true,
        level: 2,
      },
      {
        name: 'Manager',
        slug: 'manager',
        description: 'Management access',
        is_system: true,
        level: 3,
      },
      {
        name: 'Executive',
        slug: 'executive',
        description: 'Executive access',
        is_system: true,
        level: 4,
      },
      {
        name: 'User',
        slug: 'user',
        description: 'Basic user access',
        is_system: true,
        level: 5,
      },
    ];
  };

  // Instance methods
  Role.prototype.hasPermission = async function(permissionSlug) {
    const rolePermissions = await this.getPermissions();
    return rolePermissions.some(permission => permission.slug === permissionSlug);
  };

  // Associations
  Role.associate = function(models) {
    Role.belongsToMany(models.User, {
      through: models.UserRole,
      foreignKey: 'role_id',
      otherKey: 'user_id',
      as: 'users',
    });

    Role.belongsToMany(models.Permission, {
      through: models.RolePermission,
      foreignKey: 'role_id',
      otherKey: 'permission_id',
      as: 'permissions',
    });
  };

  return Role;
}
