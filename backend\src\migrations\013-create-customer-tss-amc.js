import { DataTypes } from 'sequelize';

export const up = async (queryInterface) => {
  // Create customer_tss table
  await queryInterface.createTable('customer_tss', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    customer_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'customers',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    license_edition_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'license_editions',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'RESTRICT',
    },
    tss_id: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    license_key: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    installation_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    activation_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    expiry_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    renewal_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    status: {
      type: DataTypes.ENUM('active', 'expired', 'suspended', 'cancelled'),
      allowNull: false,
      defaultValue: 'active',
    },
    version: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    installation_path: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    data_path: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    companies_count: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0,
    },
    users_count: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 1,
    },
    last_backup_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    backup_location: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    auto_backup_enabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    remote_access_enabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    tally_net_enabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    gst_enabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    payroll_enabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    pos_enabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    system_requirements: {
      type: DataTypes.JSONB,
      defaultValue: {},
    },
    installation_notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    configuration_details: {
      type: DataTypes.JSONB,
      defaultValue: {},
    },
    addons: {
      type: DataTypes.JSONB,
      defaultValue: [],
    },
    customizations: {
      type: DataTypes.JSONB,
      defaultValue: [],
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  });

  // Create customer_amc table
  await queryInterface.createTable('customer_amc', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    customer_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'customers',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    tss_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'customer_tss',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    amc_number: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    contract_type: {
      type: DataTypes.ENUM('standard', 'premium', 'enterprise', 'custom'),
      allowNull: false,
      defaultValue: 'standard',
    },
    start_date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
    },
    end_date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
    },
    renewal_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    contract_value: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
    },
    payment_terms: {
      type: DataTypes.ENUM('annual', 'semi_annual', 'quarterly', 'monthly'),
      allowNull: false,
      defaultValue: 'annual',
    },
    payment_status: {
      type: DataTypes.ENUM('paid', 'pending', 'overdue', 'partial'),
      allowNull: false,
      defaultValue: 'pending',
    },
    last_payment_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    next_payment_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    status: {
      type: DataTypes.ENUM('active', 'expired', 'suspended', 'cancelled', 'renewed'),
      allowNull: false,
      defaultValue: 'active',
    },
    services_included: {
      type: DataTypes.JSONB,
      defaultValue: [],
    },
    service_limits: {
      type: DataTypes.JSONB,
      defaultValue: {},
    },
    calls_allowed: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    calls_used: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    onsite_visits_allowed: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    onsite_visits_used: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    remote_support_hours: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    remote_support_used: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    training_hours: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    training_used: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    response_time_sla: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    resolution_time_sla: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    coverage_hours: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: '9 AM to 6 PM',
    },
    coverage_days: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: 'Monday to Saturday',
    },
    escalation_matrix: {
      type: DataTypes.JSONB,
      defaultValue: [],
    },
    exclusions: {
      type: DataTypes.JSONB,
      defaultValue: [],
    },
    special_terms: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    auto_renewal: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    renewal_notice_days: {
      type: DataTypes.INTEGER,
      defaultValue: 30,
    },
    discount_percentage: {
      type: DataTypes.DECIMAL(5, 2),
      defaultValue: 0.00,
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    created_by: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  });

  // Add indexes for customer_tss
  await queryInterface.addIndex('customer_tss', ['customer_id']);
  await queryInterface.addIndex('customer_tss', ['tss_id'], { unique: true });
  await queryInterface.addIndex('customer_tss', ['license_edition_id']);
  await queryInterface.addIndex('customer_tss', ['status']);
  await queryInterface.addIndex('customer_tss', ['expiry_date']);
  await queryInterface.addIndex('customer_tss', ['renewal_date']);
  await queryInterface.addIndex('customer_tss', ['is_active']);

  // Add indexes for customer_amc
  await queryInterface.addIndex('customer_amc', ['customer_id']);
  await queryInterface.addIndex('customer_amc', ['tss_id']);
  await queryInterface.addIndex('customer_amc', ['amc_number'], { unique: true });
  await queryInterface.addIndex('customer_amc', ['status']);
  await queryInterface.addIndex('customer_amc', ['start_date']);
  await queryInterface.addIndex('customer_amc', ['end_date']);
  await queryInterface.addIndex('customer_amc', ['renewal_date']);
  await queryInterface.addIndex('customer_amc', ['payment_status']);
  await queryInterface.addIndex('customer_amc', ['is_active']);
};

export const down = async (queryInterface) => {
  await queryInterface.dropTable('customer_amc');
  await queryInterface.dropTable('customer_tss');
};
