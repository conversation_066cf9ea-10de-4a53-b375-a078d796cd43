import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const Customer = sequelize.define('Customer', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
    },
    customer_code: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [2, 20],
      },
    },
    company_name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [2, 200],
      },
    },
    display_name: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Short display name for the customer',
    },
    industry_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'industries',
        key: 'id',
      },
    },
    area_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'areas',
        key: 'id',
      },
    },
    assigned_executive_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'executives',
        key: 'id',
      },
    },
    customer_type: {
      type: DataTypes.ENUM('prospect', 'customer', 'inactive', 'blacklisted'),
      allowNull: false,
      defaultValue: 'prospect',
    },
    business_type: {
      type: DataTypes.ENUM('proprietorship', 'partnership', 'private_limited', 'public_limited', 'llp', 'trust', 'society', 'other'),
      allowNull: true,
    },
    address_line_1: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    address_line_2: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    city: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    state: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    country: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: 'India',
    },
    postal_code: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    phone: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        isEmail: true,
      },
    },
    website: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        isUrl: true,
      },
    },
    gst_number: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [15, 15],
      },
    },
    pan_number: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [10, 10],
      },
    },
    annual_turnover: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: true,
    },
    employee_count: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    credit_limit: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    credit_days: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0,
    },
    payment_terms: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    bank_name: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    bank_account_number: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    bank_ifsc_code: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    coordinates: {
      type: DataTypes.JSONB,
      allowNull: true,
      comment: 'Geographic coordinates for mapping',
    },
    lead_source: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Source of the lead (referral, website, etc.)',
    },
    referred_by: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Name of the person who referred this customer',
    },
    first_contact_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    last_contact_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    next_follow_up_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    tags: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Array of tags for categorization',
    },
    custom_fields: {
      type: DataTypes.JSONB,
      defaultValue: {},
      comment: 'Custom fields for additional data',
    },
    created_by: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'customers',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['tenant_id'],
      },
      {
        fields: ['tenant_id', 'customer_code'],
        unique: true,
      },
      {
        fields: ['company_name'],
      },
      {
        fields: ['customer_type'],
      },
      {
        fields: ['industry_id'],
      },
      {
        fields: ['area_id'],
      },
      {
        fields: ['assigned_executive_id'],
      },
      {
        fields: ['gst_number'],
      },
      {
        fields: ['pan_number'],
      },
      {
        fields: ['is_active'],
      },
      {
        fields: ['created_by'],
      },
    ],
  });

  // Instance methods
  Customer.prototype.getFullAddress = function() {
    const parts = [
      this.address_line_1,
      this.address_line_2,
      this.city,
      this.state,
      this.country,
      this.postal_code,
    ].filter(Boolean);
    return parts.join(', ');
  };

  Customer.prototype.isProspect = function() {
    return this.customer_type === 'prospect';
  };

  Customer.prototype.isCustomer = function() {
    return this.customer_type === 'customer';
  };

  Customer.prototype.getCreditUtilization = async function() {
    // This would calculate credit utilization based on outstanding invoices
    // Implementation would require invoice/payment models
    return 0;
  };

  // Associations
  Customer.associate = function(models) {
    Customer.belongsTo(models.Tenant, {
      foreignKey: 'tenant_id',
      as: 'tenant',
    });

    Customer.belongsTo(models.Industry, {
      foreignKey: 'industry_id',
      as: 'industry',
    });

    Customer.belongsTo(models.Area, {
      foreignKey: 'area_id',
      as: 'area',
    });

    Customer.belongsTo(models.Executive, {
      foreignKey: 'assigned_executive_id',
      as: 'assignedExecutive',
    });

    Customer.belongsTo(models.User, {
      foreignKey: 'created_by',
      as: 'creator',
    });

    Customer.hasMany(models.CustomerContact, {
      foreignKey: 'customer_id',
      as: 'contacts',
    });

    Customer.hasMany(models.CustomerTSS, {
      foreignKey: 'customer_id',
      as: 'tssDetails',
    });

    Customer.hasMany(models.CustomerAMC, {
      foreignKey: 'customer_id',
      as: 'amcContracts',
    });

    Customer.hasMany(models.ServiceCall, {
      foreignKey: 'customer_id',
      as: 'serviceCalls',
    });

    Customer.hasMany(models.Sale, {
      foreignKey: 'customer_id',
      as: 'sales',
    });

    // TODO: Create Referral model
    // Customer.hasMany(models.Referral, {
    //   foreignKey: 'customer_id',
    //   as: 'referrals',
    // });
  };

  return Customer;
}
