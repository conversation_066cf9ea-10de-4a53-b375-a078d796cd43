// Using built-in fetch (Node.js 18+)

const testLoginAPI = async () => {
  console.log('🧪 Testing login API...');

  try {
    const response = await fetch('http://localhost:3001/api/v1/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    const data = await response.json();
    console.log('Response data:', JSON.stringify(data, null, 2));

    if (response.ok) {
      console.log('✅ Login API test successful!');
      console.log('Token received:', data.token ? 'Yes' : 'No');
      console.log('User data received:', data.user ? 'Yes' : 'No');
    } else {
      console.log('❌ Login API test failed');
    }

  } catch (error) {
    console.error('❌ Login API test error:', error.message);
  }
};

testLoginAPI();
