import models from '../models/index.js';
import { logger } from '../utils/logger.js';
import { Op } from 'sequelize';

/**
 * Get all executives with pagination and filters
 */
export const getExecutives = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search,
      department,
      isActive,
      sortBy = 'created_at',
      sortOrder = 'DESC',
    } = req.query;

    const offset = (page - 1) * limit;
    const where = { tenant_id: req.user.tenant.id };

    // Apply filters
    if (search) {
      where[Op.or] = [
        { first_name: { [Op.iLike]: `%${search}%` } },
        { last_name: { [Op.iLike]: `%${search}%` } },
        { employee_code: { [Op.iLike]: `%${search}%` } },
        { email: { [Op.iLike]: `%${search}%` } },
        { phone: { [Op.iLike]: `%${search}%` } },
      ];
    }

    if (department) {
      where.department = department;
    }

    if (isActive !== undefined) {
      where.is_active = isActive === 'true';
    }

    const { count, rows: executives } = await models.Executive.findAndCountAll({
      where,
      include: [
        {
          model: models.User,
          as: 'user',
          attributes: ['id', 'email', 'is_active', 'last_login_at'],
        },
        {
          model: models.Designation,
          as: 'designation',
          attributes: ['id', 'name', 'code', 'level'],
        },
        {
          model: models.StaffRole,
          as: 'staffRole',
          attributes: ['id', 'name', 'code', 'department'],
        },
      ],
      limit: parseInt(limit),
      offset,
      order: [[sortBy, sortOrder.toUpperCase()]],
    });

    res.json({
      success: true,
      data: {
        executives,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: parseInt(limit),
        },
      },
    });

  } catch (error) {
    logger.error('Get executives error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch executives',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get executive by ID
 */
export const getExecutiveById = async (req, res) => {
  try {
    const { id } = req.params;

    const executive = await models.Executive.findOne({
      where: {
        id,
        tenant_id: req.user.tenant.id,
      },
      include: [
        {
          model: models.User,
          as: 'user',
          attributes: ['id', 'email', 'is_active', 'is_verified', 'last_login_at', 'preferences'],
        },
        {
          model: models.Designation,
          as: 'designation',
        },
        {
          model: models.StaffRole,
          as: 'staffRole',
        },
      ],
    });

    if (!executive) {
      return res.status(404).json({
        success: false,
        message: 'Executive not found',
      });
    }

    // Get executive statistics
    const [assignedCustomers, serviceCalls, sales] = await Promise.all([
      models.Customer.count({
        where: {
          assigned_executive_id: id,
          tenant_id: req.user.tenant.id,
        },
      }),
      models.ServiceCall.count({
        where: {
          assigned_to: id,
          tenant_id: req.user.tenant.id,
        },
      }),
      models.Sale.count({
        where: {
          sales_executive_id: id,
          tenant_id: req.user.tenant.id,
        },
      }),
    ]);

    const executiveData = {
      ...executive.toJSON(),
      statistics: {
        assignedCustomers,
        serviceCalls,
        sales,
      },
    };

    res.json({
      success: true,
      data: { executive: executiveData },
    });

  } catch (error) {
    logger.error('Get executive by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch executive',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Create new executive
 */
export const createExecutive = async (req, res) => {
  try {
    const executiveData = {
      ...req.body,
      tenant_id: req.user.tenant.id,
    };

    // Generate employee code if not provided
    if (!executiveData.employee_code) {
      const lastExecutive = await models.Executive.findOne({
        where: { tenant_id: req.user.tenant.id },
        order: [['created_at', 'DESC']],
      });

      const nextNumber = lastExecutive ? 
        parseInt(lastExecutive.employee_code.replace(/\D/g, '')) + 1 : 1;
      executiveData.employee_code = `EMP${nextNumber.toString().padStart(3, '0')}`;
    }

    // Check if employee code already exists
    const existingExecutive = await models.Executive.findOne({
      where: {
        tenant_id: req.user.tenant.id,
        employee_code: executiveData.employee_code,
      },
    });

    if (existingExecutive) {
      return res.status(409).json({
        success: false,
        message: 'Employee code already exists',
      });
    }

    // Check if email already exists (if provided)
    if (executiveData.email) {
      const existingEmail = await models.Executive.findOne({
        where: {
          tenant_id: req.user.tenant.id,
          email: executiveData.email,
        },
      });

      if (existingEmail) {
        return res.status(409).json({
          success: false,
          message: 'Email already exists',
        });
      }
    }

    const executive = await models.Executive.create(executiveData);

    // Fetch the created executive with associations
    const createdExecutive = await models.Executive.findByPk(executive.id, {
      include: [
        {
          model: models.User,
          as: 'user',
          attributes: ['id', 'email', 'is_active'],
        },
        {
          model: models.Designation,
          as: 'designation',
        },
        {
          model: models.StaffRole,
          as: 'staffRole',
        },
      ],
    });

    logger.info('Executive created successfully:', {
      executiveId: executive.id,
      employeeCode: executive.employee_code,
      name: `${executive.first_name} ${executive.last_name}`,
      createdBy: req.user.id,
      tenantId: req.user.tenant.id,
    });

    res.status(201).json({
      success: true,
      message: 'Executive created successfully',
      data: { executive: createdExecutive },
    });

  } catch (error) {
    logger.error('Create executive error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create executive',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Update executive
 */
export const updateExecutive = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const executive = await models.Executive.findOne({
      where: {
        id,
        tenant_id: req.user.tenant.id,
      },
    });

    if (!executive) {
      return res.status(404).json({
        success: false,
        message: 'Executive not found',
      });
    }

    // Check if employee code is being changed and if it already exists
    if (updateData.employee_code && updateData.employee_code !== executive.employee_code) {
      const existingExecutive = await models.Executive.findOne({
        where: {
          tenant_id: req.user.tenant.id,
          employee_code: updateData.employee_code,
          id: { [Op.ne]: id },
        },
      });

      if (existingExecutive) {
        return res.status(409).json({
          success: false,
          message: 'Employee code already exists',
        });
      }
    }

    // Check if email is being changed and if it already exists
    if (updateData.email && updateData.email !== executive.email) {
      const existingEmail = await models.Executive.findOne({
        where: {
          tenant_id: req.user.tenant.id,
          email: updateData.email,
          id: { [Op.ne]: id },
        },
      });

      if (existingEmail) {
        return res.status(409).json({
          success: false,
          message: 'Email already exists',
        });
      }
    }

    await executive.update(updateData);

    // Fetch updated executive with associations
    const updatedExecutive = await models.Executive.findByPk(executive.id, {
      include: [
        {
          model: models.User,
          as: 'user',
          attributes: ['id', 'email', 'is_active'],
        },
        {
          model: models.Designation,
          as: 'designation',
        },
        {
          model: models.StaffRole,
          as: 'staffRole',
        },
      ],
    });

    logger.info('Executive updated successfully:', {
      executiveId: executive.id,
      employeeCode: executive.employee_code,
      updatedBy: req.user.id,
    });

    res.json({
      success: true,
      message: 'Executive updated successfully',
      data: { executive: updatedExecutive },
    });

  } catch (error) {
    logger.error('Update executive error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update executive',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Delete executive
 */
export const deleteExecutive = async (req, res) => {
  try {
    const { id } = req.params;

    const executive = await models.Executive.findOne({
      where: {
        id,
        tenant_id: req.user.tenant.id,
      },
    });

    if (!executive) {
      return res.status(404).json({
        success: false,
        message: 'Executive not found',
      });
    }

    // Check if executive has related records
    const [customersCount, serviceCallsCount, salesCount] = await Promise.all([
      models.Customer.count({ where: { assigned_executive_id: id } }),
      models.ServiceCall.count({ where: { assigned_to: id } }),
      models.Sale.count({ where: { sales_executive_id: id } }),
    ]);

    if (customersCount > 0 || serviceCallsCount > 0 || salesCount > 0) {
      return res.status(409).json({
        success: false,
        message: 'Cannot delete executive with existing assignments',
        details: {
          customersCount,
          serviceCallsCount,
          salesCount,
        },
      });
    }

    await executive.destroy();

    logger.info('Executive deleted successfully:', {
      executiveId: executive.id,
      employeeCode: executive.employee_code,
      deletedBy: req.user.id,
    });

    res.json({
      success: true,
      message: 'Executive deleted successfully',
    });

  } catch (error) {
    logger.error('Delete executive error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete executive',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get executive statistics
 */
export const getExecutiveStats = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;

    const stats = await Promise.all([
      // Total executives
      models.Executive.count({
        where: { tenant_id: tenantId, is_active: true },
      }),
      
      // Executives by department
      models.Executive.findAll({
        where: { tenant_id: tenantId, is_active: true },
        attributes: [
          'department',
          [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
        ],
        group: ['department'],
        raw: true,
      }),
      
      // Recent executives (last 30 days)
      models.Executive.count({
        where: {
          tenant_id: tenantId,
          is_active: true,
          created_at: {
            [Op.gte]: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          },
        },
      }),
    ]);

    const [totalExecutives, executivesByDepartment, recentExecutives] = stats;

    res.json({
      success: true,
      data: {
        totalExecutives,
        recentExecutives,
        executivesByDepartment: executivesByDepartment.reduce((acc, item) => {
          acc[item.department] = parseInt(item.count);
          return acc;
        }, {}),
      },
    });

  } catch (error) {
    logger.error('Get executive stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch executive statistics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};
