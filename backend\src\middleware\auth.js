import { verifyToken, extractTokenFromHeader } from '../utils/jwt.js';
import { logger } from '../utils/logger.js';

/**
 * Authentication middleware to verify JWT tokens
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
export const authenticate = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = extractTokenFromHeader(authHeader);

    if (!token) {
      return res.status(401).json({
        status: 'error',
        message: 'Access token is required',
        code: 'NO_TOKEN'
      });
    }

    // Verify the token
    const decoded = verifyToken(token);

    // Add user info to request object
    req.user = {
      id: decoded.id,
      email: decoded.email,
      tenantId: decoded.tenantId,
      tenant: {
        id: decoded.tenantId
      },
      roles: decoded.roles || []
    };

    // Add token to request for potential logout functionality
    req.token = token;

    next();
  } catch (error) {
    logger.warn('Authentication failed:', {
      error: error.message,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });

    if (error.message === 'Token has expired') {
      return res.status(401).json({
        status: 'error',
        message: 'Access token has expired',
        code: 'TOKEN_EXPIRED'
      });
    }

    if (error.message === 'Invalid token') {
      return res.status(401).json({
        status: 'error',
        message: 'Invalid access token',
        code: 'INVALID_TOKEN'
      });
    }

    return res.status(401).json({
      status: 'error',
      message: 'Authentication failed',
      code: 'AUTH_FAILED'
    });
  }
};

// Export authenticateToken as an alias for authenticate to maintain backward compatibility
export const authenticateToken = authenticate;

/**
 * Authorization middleware to check user permissions
 * @param {Array|string} requiredPermissions - Required permissions
 * @returns {Function} Express middleware function
 */
export const authorize = (requiredPermissions) => {
  return (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'Authentication required',
          code: 'AUTH_REQUIRED'
        });
      }

      const userPermissions = req.user.permissions || [];
      const permissions = Array.isArray(requiredPermissions)
        ? requiredPermissions
        : [requiredPermissions];

      // Check if user has any of the required permissions
      const hasPermission = permissions.some(permission =>
        userPermissions.includes(permission)
      );

      if (!hasPermission) {
        logger.warn('Authorization failed:', {
          userId: req.user.id,
          requiredPermissions: permissions,
          userPermissions,
          ip: req.ip
        });

        return res.status(403).json({
          status: 'error',
          message: 'Insufficient permissions',
          code: 'INSUFFICIENT_PERMISSIONS',
          required: permissions
        });
      }

      next();
    } catch (error) {
      logger.error('Authorization error:', error);
      return res.status(500).json({
        status: 'error',
        message: 'Authorization check failed',
        code: 'AUTH_CHECK_FAILED'
      });
    }
  };
};

/**
 * Role-based authorization middleware
 * @param {Array|string} requiredRoles - Required roles
 * @returns {Function} Express middleware function
 */
export const requireRole = (requiredRoles) => {
  return (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'Authentication required',
          code: 'AUTH_REQUIRED'
        });
      }

      const userRoles = req.user.roles || [];
      const roles = Array.isArray(requiredRoles)
        ? requiredRoles
        : [requiredRoles];

      // Check if user has any of the required roles
      const hasRole = roles.some(role =>
        userRoles.some(userRole =>
          userRole.name === role || userRole === role
        )
      );

      if (!hasRole) {
        logger.warn('Role authorization failed:', {
          userId: req.user.id,
          requiredRoles: roles,
          userRoles,
          ip: req.ip
        });

        return res.status(403).json({
          status: 'error',
          message: 'Insufficient role permissions',
          code: 'INSUFFICIENT_ROLE',
          required: roles
        });
      }

      next();
    } catch (error) {
      logger.error('Role authorization error:', error);
      return res.status(500).json({
        status: 'error',
        message: 'Role check failed',
        code: 'ROLE_CHECK_FAILED'
      });
    }
  };
};

/**
 * Tenant isolation middleware
 * Ensures users can only access data from their tenant
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
export const requireTenant = (req, res, next) => {
  try {
    if (!req.user || !req.user.tenantId) {
      return res.status(401).json({
        status: 'error',
        message: 'Tenant information required',
        code: 'TENANT_REQUIRED'
      });
    }

    // Add tenant filter to request for query building
    req.tenantFilter = {
      tenant_id: req.user.tenantId
    };

    next();
  } catch (error) {
    logger.error('Tenant isolation error:', error);
    return res.status(500).json({
      status: 'error',
      message: 'Tenant check failed',
      code: 'TENANT_CHECK_FAILED'
    });
  }
};

/**
 * Optional authentication middleware
 * Verifies token if present but doesn't require it
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
export const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = extractTokenFromHeader(authHeader);

    if (token) {
      try {
        const decoded = verifyToken(token);
        req.user = {
          id: decoded.id,
          email: decoded.email,
          tenantId: decoded.tenantId,
          tenant: {
            id: decoded.tenantId
          },
          roles: decoded.roles || []
        };
        req.token = token;
      } catch (error) {
        // Token is invalid but we don't fail the request
        logger.debug('Optional auth failed:', error.message);
      }
    }

    next();
  } catch (error) {
    logger.error('Optional auth error:', error);
    next(); // Continue even if there's an error
  }
};

/**
 * Middleware to check tenant access permission
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
export const requireTenantAccess = (req, res, next) => {
  try {
    if (!req.user || !req.user.tenantId) {
      return res.status(403).json({
        status: 'error',
        message: 'Tenant access is required',
        code: 'TENANT_ACCESS_DENIED'
      });
    }

    next();
  } catch (error) {
    logger.error('Tenant access check failed:', {
      error: error.message,
      userId: req.user?.id,
      ip: req.ip
    });

    return res.status(500).json({
      status: 'error',
      message: 'Server error during tenant access check',
      code: 'SERVER_ERROR'
    });
  }
};

/**
 * Middleware to check if user has required permission
 * @param {string|string[]} permission - Required permission or array of permissions
 * @returns {Function} Express middleware function
 */
export const requirePermission = (permission) => {
  return (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          status: 'error',
          message: 'Authentication required',
          code: 'AUTH_REQUIRED'
        });
      }

      // For simplicity during development, allow all authenticated users
      // TODO: Implement proper permission checking with roles
      return next();

    } catch (error) {
      logger.error('Permission check failed:', {
        error: error.message,
        userId: req.user?.id,
        permission,
        ip: req.ip
      });

      return res.status(500).json({
        status: 'error',
        message: 'Server error during permission check',
        code: 'SERVER_ERROR'
      });
    }
  };
};
