import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import ServiceList from './ServiceList';
import ServiceForm from './ServiceForm';
import ServiceDetails from './ServiceDetails';

const ServiceRoutes = () => {
  return (
    <Routes>
      <Route index element={<ServiceList />} />
      <Route path="add" element={<ServiceForm />} />
      <Route path=":id" element={<ServiceDetails />} />
      <Route path=":id/edit" element={<ServiceForm />} />
      <Route path="*" element={<Navigate to="/services" replace />} />
    </Routes>
  );
};

export default ServiceRoutes;
