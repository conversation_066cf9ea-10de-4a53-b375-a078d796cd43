import fetch from 'node-fetch';

const testAPI = async () => {
  try {
    console.log('Testing API endpoints...');
    
    // Test health endpoint
    const healthResponse = await fetch('http://localhost:3001/health');
    const healthData = await healthResponse.json();
    console.log('Health endpoint:', healthData);
    
    // Test auth login endpoint with POST
    const loginResponse = await fetch('http://localhost:3001/api/v1/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      })
    });
    
    const loginData = await loginResponse.json();
    console.log('Login endpoint response:', loginData);
    
    // Test customers endpoint
    const customersResponse = await fetch('http://localhost:3001/api/v1/customers');
    const customersData = await customersResponse.json();
    console.log('Customers endpoint:', customersData);
    
  } catch (error) {
    console.error('API test failed:', error);
  }
};

testAPI();
