import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { 
  FaPlus, 
  FaEdit, 
  FaEye, 
  FaTrash, 
  FaSearch, 
  FaFilter,
  FaDownload,
  FaPhone,
  FaEnvelope,
  FaMapMarkerAlt
} from 'react-icons/fa';

const CustomerList = () => {
  const navigate = useNavigate();
  const [customers, setCustomers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [customersPerPage] = useState(10);

  // Mock data - replace with API call
  useEffect(() => {
    const mockCustomers = [
      {
        id: 1,
        name: 'ABC Enterprises',
        contactPerson: '<PERSON>',
        email: '<EMAIL>',
        phone: '+91 9876543210',
        city: 'Mumbai',
        state: 'Maharashtra',
        status: 'active',
        tallyVersion: 'Prime',
        lastContact: '2024-01-15',
        totalServices: 12,
        pendingAmount: 25000
      },
      {
        id: 2,
        name: 'XYZ Trading Co.',
        contactPerson: 'Jane Smith',
        email: '<EMAIL>',
        phone: '+91 9876543211',
        city: 'Delhi',
        state: 'Delhi',
        status: 'active',
        tallyVersion: 'Silver',
        lastContact: '2024-01-10',
        totalServices: 8,
        pendingAmount: 15000
      },
      {
        id: 3,
        name: 'PQR Industries',
        contactPerson: 'Mike Johnson',
        email: '<EMAIL>',
        phone: '+91 9876543212',
        city: 'Bangalore',
        state: 'Karnataka',
        status: 'inactive',
        tallyVersion: 'Gold',
        lastContact: '2023-12-20',
        totalServices: 15,
        pendingAmount: 0
      }
    ];
    
    setTimeout(() => {
      setCustomers(mockCustomers);
      setLoading(false);
    }, 1000);
  }, []);

  // Filter customers based on search and status
  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.contactPerson.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || customer.status === filterStatus;
    return matchesSearch && matchesStatus;
  });

  // Pagination
  const indexOfLastCustomer = currentPage * customersPerPage;
  const indexOfFirstCustomer = indexOfLastCustomer - customersPerPage;
  const currentCustomers = filteredCustomers.slice(indexOfFirstCustomer, indexOfLastCustomer);
  const totalPages = Math.ceil(filteredCustomers.length / customersPerPage);

  const handleDelete = (customerId) => {
    if (window.confirm('Are you sure you want to delete this customer?')) {
      setCustomers(customers.filter(customer => customer.id !== customerId));
      toast.success('Customer deleted successfully');
    }
  };

  const getStatusBadge = (status) => {
    const badgeClass = status === 'active' ? 'bg-success' : 'bg-secondary';
    return <span className={`badge ${badgeClass}`}>{status.toUpperCase()}</span>;
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container-fluid">
      {/* Header */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h2 className="mb-0">Customer Management</h2>
              <p className="text-muted">Manage your customer database</p>
            </div>
            <div className="d-flex gap-2">
              <button className="btn btn-outline-primary">
                <FaDownload className="me-2" />
                Export
              </button>
              <Link to="/customers/add" className="btn btn-primary">
                <FaPlus className="me-2" />
                Add Customer
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="row mb-4">
        <div className="col-md-6">
          <div className="input-group">
            <span className="input-group-text">
              <FaSearch />
            </span>
            <input
              type="text"
              className="form-control"
              placeholder="Search customers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
        <div className="col-md-3">
          <select
            className="form-select"
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>
        </div>
        <div className="col-md-3">
          <button className="btn btn-outline-secondary w-100">
            <FaFilter className="me-2" />
            More Filters
          </button>
        </div>
      </div>

      {/* Customer Table */}
      <div className="row">
        <div className="col-12">
          <div className="card">
            <div className="card-body">
              <div className="table-responsive">
                <table className="table table-hover">
                  <thead className="table-light">
                    <tr>
                      <th>Customer</th>
                      <th>Contact</th>
                      <th>Location</th>
                      <th>Tally Version</th>
                      <th>Status</th>
                      <th>Services</th>
                      <th>Pending Amount</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {currentCustomers.map(customer => (
                      <tr key={customer.id}>
                        <td>
                          <div>
                            <h6 className="mb-0">{customer.name}</h6>
                            <small className="text-muted">{customer.contactPerson}</small>
                          </div>
                        </td>
                        <td>
                          <div>
                            <div className="d-flex align-items-center mb-1">
                              <FaPhone className="text-muted me-2" size={12} />
                              <small>{customer.phone}</small>
                            </div>
                            <div className="d-flex align-items-center">
                              <FaEnvelope className="text-muted me-2" size={12} />
                              <small>{customer.email}</small>
                            </div>
                          </div>
                        </td>
                        <td>
                          <div className="d-flex align-items-center">
                            <FaMapMarkerAlt className="text-muted me-2" size={12} />
                            <small>{customer.city}, {customer.state}</small>
                          </div>
                        </td>
                        <td>
                          <span className="badge bg-info">{customer.tallyVersion}</span>
                        </td>
                        <td>{getStatusBadge(customer.status)}</td>
                        <td>
                          <span className="badge bg-primary">{customer.totalServices}</span>
                        </td>
                        <td>
                          <span className={`fw-bold ${customer.pendingAmount > 0 ? 'text-danger' : 'text-success'}`}>
                            ₹{customer.pendingAmount.toLocaleString()}
                          </span>
                        </td>
                        <td>
                          <div className="btn-group" role="group">
                            <button
                              className="btn btn-sm btn-outline-primary"
                              onClick={() => navigate(`/customers/${customer.id}`)}
                              title="View Details"
                            >
                              <FaEye />
                            </button>
                            <button
                              className="btn btn-sm btn-outline-secondary"
                              onClick={() => navigate(`/customers/${customer.id}/edit`)}
                              title="Edit"
                            >
                              <FaEdit />
                            </button>
                            <button
                              className="btn btn-sm btn-outline-danger"
                              onClick={() => handleDelete(customer.id)}
                              title="Delete"
                            >
                              <FaTrash />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <nav className="mt-4">
                  <ul className="pagination justify-content-center">
                    <li className={`page-item ${currentPage === 1 ? 'disabled' : ''}`}>
                      <button
                        className="page-link"
                        onClick={() => setCurrentPage(currentPage - 1)}
                        disabled={currentPage === 1}
                      >
                        Previous
                      </button>
                    </li>
                    {[...Array(totalPages)].map((_, index) => (
                      <li key={index} className={`page-item ${currentPage === index + 1 ? 'active' : ''}`}>
                        <button
                          className="page-link"
                          onClick={() => setCurrentPage(index + 1)}
                        >
                          {index + 1}
                        </button>
                      </li>
                    ))}
                    <li className={`page-item ${currentPage === totalPages ? 'disabled' : ''}`}>
                      <button
                        className="page-link"
                        onClick={() => setCurrentPage(currentPage + 1)}
                        disabled={currentPage === totalPages}
                      >
                        Next
                      </button>
                    </li>
                  </ul>
                </nav>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomerList;
