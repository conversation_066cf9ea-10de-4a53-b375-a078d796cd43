import express from 'express';
import { body, query, param } from 'express-validator';
import { validate } from '../middleware/validation.js';
import { authenticateToken, requirePermission, requireTenantAccess } from '../middleware/auth.js';
import {
  getCustomers,
  getCustomerById,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  getCustomerStats,
} from '../controllers/customerController.js';

const router = express.Router();

// Apply authentication and tenant access to all routes
router.use(authenticateToken);
router.use(requireTenantAccess);

/**
 * @route   GET /api/customers
 * @desc    Get all customers with pagination and filters
 * @access  Private (requires customers.read permission)
 */
router.get('/', [
  requirePermission('customers.read'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('search')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search term must be between 1 and 100 characters'),
  query('customerType')
    .optional()
    .isIn(['prospect', 'customer', 'inactive', 'blacklisted'])
    .withMessage('Invalid customer type'),
  query('industryId')
    .optional()
    .isUUID()
    .withMessage('Industry ID must be a valid UUID'),
  query('areaId')
    .optional()
    .isUUID()
    .withMessage('Area ID must be a valid UUID'),
  query('assignedExecutiveId')
    .optional()
    .isUUID()
    .withMessage('Assigned executive ID must be a valid UUID'),
  query('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
  query('sortBy')
    .optional()
    .isIn(['created_at', 'updated_at', 'company_name', 'customer_code'])
    .withMessage('Invalid sort field'),
  query('sortOrder')
    .optional()
    .isIn(['ASC', 'DESC'])
    .withMessage('Sort order must be ASC or DESC'),
  validate,
], getCustomers);

/**
 * @route   GET /api/customers/stats
 * @desc    Get customer statistics
 * @access  Private (requires customers.read permission)
 */
router.get('/stats', [
  requirePermission('customers.read'),
], getCustomerStats);

/**
 * @route   GET /api/customers/:id
 * @desc    Get customer by ID
 * @access  Private (requires customers.read permission)
 */
router.get('/:id', [
  requirePermission('customers.read'),
  param('id')
    .isUUID()
    .withMessage('Customer ID must be a valid UUID'),
  validate,
], getCustomerById);

/**
 * @route   POST /api/customers
 * @desc    Create new customer
 * @access  Private (requires customers.create permission)
 */
router.post('/', [
  requirePermission('customers.create'),
  body('company_name')
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Company name must be between 2 and 200 characters'),
  body('customer_code')
    .optional()
    .trim()
    .isLength({ min: 2, max: 20 })
    .withMessage('Customer code must be between 2 and 20 characters'),
  body('display_name')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Display name must be between 1 and 100 characters'),
  body('customer_type')
    .optional()
    .isIn(['prospect', 'customer', 'inactive', 'blacklisted'])
    .withMessage('Invalid customer type'),
  body('business_type')
    .optional()
    .isIn(['proprietorship', 'partnership', 'private_limited', 'public_limited', 'llp', 'trust', 'society', 'other'])
    .withMessage('Invalid business type'),
  body('industry_id')
    .optional()
    .isUUID()
    .withMessage('Industry ID must be a valid UUID'),
  body('area_id')
    .optional()
    .isUUID()
    .withMessage('Area ID must be a valid UUID'),
  body('assigned_executive_id')
    .optional()
    .isUUID()
    .withMessage('Assigned executive ID must be a valid UUID'),
  body('email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('phone')
    .optional()
    .isMobilePhone()
    .withMessage('Please provide a valid phone number'),
  body('website')
    .optional()
    .isURL()
    .withMessage('Please provide a valid website URL'),
  body('gst_number')
    .optional()
    .isLength({ min: 15, max: 15 })
    .withMessage('GST number must be exactly 15 characters'),
  body('pan_number')
    .optional()
    .isLength({ min: 10, max: 10 })
    .withMessage('PAN number must be exactly 10 characters'),
  body('annual_turnover')
    .optional()
    .isDecimal()
    .withMessage('Annual turnover must be a valid decimal number'),
  body('employee_count')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Employee count must be a non-negative integer'),
  body('credit_limit')
    .optional()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('Credit limit must be a valid decimal number'),
  body('credit_days')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Credit days must be a non-negative integer'),
  body('first_contact_date')
    .optional()
    .isISO8601()
    .withMessage('First contact date must be a valid date'),
  body('next_follow_up_date')
    .optional()
    .isISO8601()
    .withMessage('Next follow up date must be a valid date'),
  validate,
], createCustomer);

/**
 * @route   PUT /api/customers/:id
 * @desc    Update customer
 * @access  Private (requires customers.update permission)
 */
router.put('/:id', [
  requirePermission('customers.update'),
  param('id')
    .isUUID()
    .withMessage('Customer ID must be a valid UUID'),
  body('company_name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Company name must be between 2 and 200 characters'),
  body('customer_code')
    .optional()
    .trim()
    .isLength({ min: 2, max: 20 })
    .withMessage('Customer code must be between 2 and 20 characters'),
  body('display_name')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Display name must be between 1 and 100 characters'),
  body('customer_type')
    .optional()
    .isIn(['prospect', 'customer', 'inactive', 'blacklisted'])
    .withMessage('Invalid customer type'),
  body('business_type')
    .optional()
    .isIn(['proprietorship', 'partnership', 'private_limited', 'public_limited', 'llp', 'trust', 'society', 'other'])
    .withMessage('Invalid business type'),
  body('industry_id')
    .optional()
    .isUUID()
    .withMessage('Industry ID must be a valid UUID'),
  body('area_id')
    .optional()
    .isUUID()
    .withMessage('Area ID must be a valid UUID'),
  body('assigned_executive_id')
    .optional()
    .isUUID()
    .withMessage('Assigned executive ID must be a valid UUID'),
  body('email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('phone')
    .optional()
    .isMobilePhone()
    .withMessage('Please provide a valid phone number'),
  body('website')
    .optional()
    .isURL()
    .withMessage('Please provide a valid website URL'),
  body('gst_number')
    .optional()
    .isLength({ min: 15, max: 15 })
    .withMessage('GST number must be exactly 15 characters'),
  body('pan_number')
    .optional()
    .isLength({ min: 10, max: 10 })
    .withMessage('PAN number must be exactly 10 characters'),
  body('annual_turnover')
    .optional()
    .isDecimal()
    .withMessage('Annual turnover must be a valid decimal number'),
  body('employee_count')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Employee count must be a non-negative integer'),
  body('credit_limit')
    .optional()
    .isDecimal({ decimal_digits: '0,2' })
    .withMessage('Credit limit must be a valid decimal number'),
  body('credit_days')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Credit days must be a non-negative integer'),
  body('first_contact_date')
    .optional()
    .isISO8601()
    .withMessage('First contact date must be a valid date'),
  body('next_follow_up_date')
    .optional()
    .isISO8601()
    .withMessage('Next follow up date must be a valid date'),
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean'),
  validate,
], updateCustomer);

/**
 * @route   DELETE /api/customers/:id
 * @desc    Delete customer
 * @access  Private (requires customers.delete permission)
 */
router.delete('/:id', [
  requirePermission('customers.delete'),
  param('id')
    .isUUID()
    .withMessage('Customer ID must be a valid UUID'),
  validate,
], deleteCustomer);

export default router;
