import { logger } from './src/utils/logger.js';

const testAuthRoutes = async () => {
  try {
    logger.info('Testing auth routes import...');
    
    // Test auth routes import
    const authRoutes = await import('./src/routes/auth.js');
    logger.info('✅ Auth routes imported successfully');
    
  } catch (error) {
    logger.error('❌ Auth routes import failed:', error);
    console.error('Full error:', error);
  }
};

testAuthRoutes();
