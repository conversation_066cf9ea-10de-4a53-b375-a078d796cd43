console.log('Starting basic test...');

import express from 'express';

console.log('Express imported successfully');

const app = express();
console.log('Express app created');

app.get('/test', (req, res) => {
  res.json({ message: 'Hello World' });
});

const PORT = 3001;
const server = app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});

console.log('Server setup complete');
