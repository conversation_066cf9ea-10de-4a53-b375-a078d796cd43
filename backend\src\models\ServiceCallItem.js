import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const ServiceCallItem = sequelize.define('ServiceCallItem', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    service_call_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'service_calls',
        key: 'id',
      },
    },
    item_type: {
      type: DataTypes.ENUM('product', 'service', 'additional_service'),
      allowNull: false,
      defaultValue: 'service',
    },
    product_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'tally_products',
        key: 'id',
      },
    },
    additional_service_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'additional_services',
        key: 'id',
      },
    },
    description: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [2, 200],
      },
    },
    quantity: {
      type: DataTypes.DECIMAL(10, 3),
      allowNull: false,
      defaultValue: 1.000,
    },
    unit: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'Nos',
    },
    rate: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
    },
    amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
    },
    discount_percentage: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    discount_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    taxable_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
    },
    gst_rate: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: false,
      defaultValue: 18.00,
    },
    cgst_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    sgst_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    igst_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    total_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
    },
    is_billable: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    is_completed: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    completion_notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'service_call_items',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['service_call_id'],
      },
      {
        fields: ['item_type'],
      },
      {
        fields: ['product_id'],
      },
      {
        fields: ['additional_service_id'],
      },
      {
        fields: ['is_billable'],
      },
      {
        fields: ['is_completed'],
      },
    ],
  });

  // Instance methods
  ServiceCallItem.prototype.calculateAmount = function() {
    return parseFloat(this.quantity) * parseFloat(this.rate);
  };

  ServiceCallItem.prototype.calculateDiscountAmount = function() {
    const amount = this.calculateAmount();
    if (this.discount_percentage > 0) {
      return (amount * parseFloat(this.discount_percentage)) / 100;
    }
    return parseFloat(this.discount_amount) || 0;
  };

  ServiceCallItem.prototype.calculateTaxableAmount = function() {
    const amount = this.calculateAmount();
    const discountAmount = this.calculateDiscountAmount();
    return amount - discountAmount;
  };

  ServiceCallItem.prototype.calculateGSTAmount = function() {
    const taxableAmount = this.calculateTaxableAmount();
    return (taxableAmount * parseFloat(this.gst_rate)) / 100;
  };

  ServiceCallItem.prototype.calculateTotalAmount = function() {
    const taxableAmount = this.calculateTaxableAmount();
    const gstAmount = this.calculateGSTAmount();
    return taxableAmount + gstAmount;
  };

  ServiceCallItem.prototype.updateCalculatedFields = function() {
    this.amount = this.calculateAmount();
    this.discount_amount = this.calculateDiscountAmount();
    this.taxable_amount = this.calculateTaxableAmount();
    
    const gstAmount = this.calculateGSTAmount();
    // For intra-state transactions (CGST + SGST)
    this.cgst_amount = gstAmount / 2;
    this.sgst_amount = gstAmount / 2;
    this.igst_amount = 0;
    
    // For inter-state transactions, use IGST instead
    // this.igst_amount = gstAmount;
    // this.cgst_amount = 0;
    // this.sgst_amount = 0;
    
    this.total_amount = this.calculateTotalAmount();
  };

  // Associations
  ServiceCallItem.associate = function(models) {
    ServiceCallItem.belongsTo(models.ServiceCall, {
      foreignKey: 'service_call_id',
      as: 'serviceCall',
    });

    ServiceCallItem.belongsTo(models.TallyProduct, {
      foreignKey: 'product_id',
      as: 'product',
    });

    ServiceCallItem.belongsTo(models.AdditionalService, {
      foreignKey: 'additional_service_id',
      as: 'additionalService',
    });
  };

  return ServiceCallItem;
}
