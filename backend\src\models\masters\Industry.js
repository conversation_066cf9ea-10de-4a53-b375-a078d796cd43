import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const Industry = sequelize.define('Industry', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [2, 100],
      },
    },
    code: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        len: [2, 20],
      },
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    category: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Industry category or sector',
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    sort_order: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'industries',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['code'],
        unique: true,
      },
      {
        fields: ['category'],
      },
      {
        fields: ['is_active'],
      },
      {
        fields: ['sort_order'],
      },
    ],
  });

  // Class methods
  Industry.getDefaultIndustries = function() {
    return [
      {
        name: 'Manufacturing',
        code: 'MANUFACTURING',
        description: 'Manufacturing and production companies',
        category: 'Production',
        sort_order: 1,
      },
      {
        name: 'Trading',
        code: 'TRADING',
        description: 'Trading and distribution companies',
        category: 'Commerce',
        sort_order: 2,
      },
      {
        name: 'Services',
        code: 'SERVICES',
        description: 'Service-based companies',
        category: 'Services',
        sort_order: 3,
      },
      {
        name: 'Retail',
        code: 'RETAIL',
        description: 'Retail and consumer goods',
        category: 'Commerce',
        sort_order: 4,
      },
      {
        name: 'Healthcare',
        code: 'HEALTHCARE',
        description: 'Healthcare and medical services',
        category: 'Services',
        sort_order: 5,
      },
      {
        name: 'Education',
        code: 'EDUCATION',
        description: 'Educational institutions',
        category: 'Services',
        sort_order: 6,
      },
      {
        name: 'Construction',
        code: 'CONSTRUCTION',
        description: 'Construction and real estate',
        category: 'Construction',
        sort_order: 7,
      },
      {
        name: 'IT Services',
        code: 'IT_SERVICES',
        description: 'Information technology services',
        category: 'Technology',
        sort_order: 8,
      },
      {
        name: 'Hospitality',
        code: 'HOSPITALITY',
        description: 'Hotels and hospitality services',
        category: 'Services',
        sort_order: 9,
      },
      {
        name: 'Transportation',
        code: 'TRANSPORTATION',
        description: 'Transportation and logistics',
        category: 'Logistics',
        sort_order: 10,
      },
      {
        name: 'Agriculture',
        code: 'AGRICULTURE',
        description: 'Agriculture and farming',
        category: 'Agriculture',
        sort_order: 11,
      },
      {
        name: 'Financial Services',
        code: 'FINANCIAL',
        description: 'Banking and financial services',
        category: 'Finance',
        sort_order: 12,
      },
      {
        name: 'Pharmaceutical',
        code: 'PHARMA',
        description: 'Pharmaceutical and drugs',
        category: 'Healthcare',
        sort_order: 13,
      },
      {
        name: 'Textile',
        code: 'TEXTILE',
        description: 'Textile and garments',
        category: 'Manufacturing',
        sort_order: 14,
      },
      {
        name: 'Automotive',
        code: 'AUTOMOTIVE',
        description: 'Automotive and vehicles',
        category: 'Manufacturing',
        sort_order: 15,
      },
    ];
  };

  // Associations
  Industry.associate = function(models) {
    Industry.hasMany(models.Customer, {
      foreignKey: 'industry_id',
      as: 'customers',
    });
  };

  return Industry;
}
