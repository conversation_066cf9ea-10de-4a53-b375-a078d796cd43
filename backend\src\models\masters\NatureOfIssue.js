import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const NatureOfIssue = sequelize.define('NatureOfIssue', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [2, 100],
      },
    },
    code: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        len: [2, 20],
      },
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    category: {
      type: DataTypes.ENUM('technical', 'functional', 'data', 'installation', 'training', 'other'),
      allowNull: false,
      defaultValue: 'technical',
    },
    severity: {
      type: DataTypes.ENUM('low', 'medium', 'high', 'critical'),
      allowNull: false,
      defaultValue: 'medium',
    },
    estimated_resolution_time: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Estimated resolution time in minutes',
    },
    requires_onsite: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Whether this issue typically requires onsite visit',
    },
    common_solutions: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Array of common solutions for this issue',
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    sort_order: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'nature_of_issues',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['code'],
        unique: true,
      },
      {
        fields: ['category'],
      },
      {
        fields: ['severity'],
      },
      {
        fields: ['requires_onsite'],
      },
      {
        fields: ['is_active'],
      },
      {
        fields: ['sort_order'],
      },
    ],
  });

  // Class methods
  NatureOfIssue.getDefaultIssues = function() {
    return [
      {
        name: 'Software Installation',
        code: 'SW_INSTALL',
        description: 'Tally software installation and setup',
        category: 'installation',
        severity: 'medium',
        estimated_resolution_time: 60,
        requires_onsite: true,
        common_solutions: [
          'Check system requirements',
          'Download latest version',
          'Run as administrator',
          'Disable antivirus temporarily',
        ],
        sort_order: 1,
      },
      {
        name: 'Data Corruption',
        code: 'DATA_CORRUPT',
        description: 'Data corruption or database issues',
        category: 'data',
        severity: 'high',
        estimated_resolution_time: 120,
        requires_onsite: false,
        common_solutions: [
          'Restore from backup',
          'Run data repair utility',
          'Reindex database',
          'Check disk space',
        ],
        sort_order: 2,
      },
      {
        name: 'Performance Issues',
        code: 'PERFORMANCE',
        description: 'Slow performance or hanging issues',
        category: 'technical',
        severity: 'medium',
        estimated_resolution_time: 90,
        requires_onsite: false,
        common_solutions: [
          'Clear temporary files',
          'Optimize database',
          'Check system resources',
          'Update software',
        ],
        sort_order: 3,
      },
      {
        name: 'GST Configuration',
        code: 'GST_CONFIG',
        description: 'GST setup and configuration issues',
        category: 'functional',
        severity: 'medium',
        estimated_resolution_time: 45,
        requires_onsite: false,
        common_solutions: [
          'Configure GST rates',
          'Set up GSTIN',
          'Enable GST features',
          'Update tax masters',
        ],
        sort_order: 4,
      },
      {
        name: 'Printing Issues',
        code: 'PRINTING',
        description: 'Report printing and formatting issues',
        category: 'technical',
        severity: 'low',
        estimated_resolution_time: 30,
        requires_onsite: false,
        common_solutions: [
          'Check printer settings',
          'Configure page setup',
          'Update printer drivers',
          'Test with different printer',
        ],
        sort_order: 5,
      },
      {
        name: 'User Training',
        code: 'TRAINING',
        description: 'User training and guidance',
        category: 'training',
        severity: 'low',
        estimated_resolution_time: 120,
        requires_onsite: true,
        common_solutions: [
          'Provide user manual',
          'Conduct training session',
          'Create video tutorials',
          'Schedule follow-up',
        ],
        sort_order: 6,
      },
      {
        name: 'License Activation',
        code: 'LICENSE',
        description: 'License activation and validation issues',
        category: 'technical',
        severity: 'high',
        estimated_resolution_time: 30,
        requires_onsite: false,
        common_solutions: [
          'Check internet connection',
          'Validate license key',
          'Contact Tally support',
          'Reactivate license',
        ],
        sort_order: 7,
      },
      {
        name: 'Data Migration',
        code: 'DATA_MIGRATION',
        description: 'Data migration from other software',
        category: 'data',
        severity: 'medium',
        estimated_resolution_time: 180,
        requires_onsite: true,
        common_solutions: [
          'Export data from source',
          'Format data properly',
          'Import using Tally tools',
          'Verify data integrity',
        ],
        sort_order: 8,
      },
      {
        name: 'Network Issues',
        code: 'NETWORK',
        description: 'Multi-user and network connectivity issues',
        category: 'technical',
        severity: 'high',
        estimated_resolution_time: 90,
        requires_onsite: true,
        common_solutions: [
          'Check network connectivity',
          'Configure firewall',
          'Set up port forwarding',
          'Test with different network',
        ],
        sort_order: 9,
      },
      {
        name: 'Backup & Restore',
        code: 'BACKUP',
        description: 'Backup and restore related issues',
        category: 'data',
        severity: 'medium',
        estimated_resolution_time: 60,
        requires_onsite: false,
        common_solutions: [
          'Set up automatic backup',
          'Test restore process',
          'Configure backup location',
          'Schedule regular backups',
        ],
        sort_order: 10,
      },
    ];
  };

  // Instance methods
  NatureOfIssue.prototype.getEstimatedHours = function() {
    return this.estimated_resolution_time ? Math.ceil(this.estimated_resolution_time / 60) : 1;
  };

  NatureOfIssue.prototype.getSeverityColor = function() {
    const colors = {
      low: '#28a745',
      medium: '#ffc107',
      high: '#fd7e14',
      critical: '#dc3545',
    };
    return colors[this.severity] || '#6c757d';
  };

  // Associations
  NatureOfIssue.associate = function(models) {
    NatureOfIssue.hasMany(models.ServiceCall, {
      foreignKey: 'nature_of_issue_id',
      as: 'serviceCalls',
    });
  };

  return NatureOfIssue;
}
