import { sequelize } from './src/utils/database.js';
import { logger } from './src/utils/logger.js';

const checkTables = async () => {
  try {
    logger.info('Checking database tables...');
    
    // Test connection
    await sequelize.authenticate();
    logger.info('✅ Database connection successful');
    
    // Get all tables
    const tables = await sequelize.getQueryInterface().showAllTables();
    logger.info('📋 Available tables:', tables);
    
    // Check specific tables
    for (const table of tables) {
      try {
        const [results] = await sequelize.query(`SELECT COUNT(*) as count FROM ${table}`);
        logger.info(`📊 Table ${table}: ${results[0].count} rows`);
      } catch (error) {
        logger.error(`❌ Error checking table ${table}:`, error.message);
      }
    }
    
    logger.info('✅ Table check completed!');
    
  } catch (error) {
    logger.error('❌ Table check failed:', error);
  } finally {
    await sequelize.close();
  }
};

checkTables();
