import { logger } from '../utils/logger.js';
import appConfig from '../../config/app.js';

// Simple request logger middleware (will be replaced with <PERSON> when dependencies are installed)
export const requestLogger = (req, res, next) => {
  const start = Date.now();
  const ip = req.ip || req.connection.remoteAddress || 'unknown';
  const userAgent = req.get('User-Agent') || 'unknown';

  // Log request
  logger.http(`${req.method} ${req.originalUrl} - ${ip} - ${userAgent}`);

  // Log response when finished
  res.on('finish', () => {
    const duration = Date.now() - start;
    const logMessage = `${req.method} ${req.originalUrl} - ${res.statusCode} - ${duration}ms - ${ip}`;

    if (res.statusCode >= 400) {
      logger.warn(logMessage);
    } else {
      logger.http(logMessage);
    }
  });

  next();
};

// Request timing middleware
export const requestTiming = (req, res, next) => {
  const start = Date.now();

  res.on('finish', () => {
    const duration = Date.now() - start;
    res.setHeader('X-Response-Time', duration);
  });

  next();
};

// Request ID middleware
export const requestId = (req, res, next) => {
  req.id = req.headers['x-request-id'] ||
           `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  res.setHeader('X-Request-ID', req.id);
  next();
};
