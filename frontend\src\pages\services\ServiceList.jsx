import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { 
  FaPlus, 
  FaEdit, 
  FaEye, 
  FaTrash, 
  FaSearch, 
  FaFilter,
  FaDownload,
  FaUser,
  FaCalendar,
  FaClock,
  FaRupeeSign,
  FaTools
} from 'react-icons/fa';

const ServiceList = () => {
  const navigate = useNavigate();
  const [services, setServices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterType, setFilterType] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [servicesPerPage] = useState(10);

  // Mock data - replace with API call
  useEffect(() => {
    const mockServices = [
      {
        id: 1,
        serviceNumber: 'SRV-2024-001',
        customer: 'ABC Enterprises',
        customerId: 1,
        contactPerson: '<PERSON>',
        type: 'Installation',
        description: 'Tally Prime Installation and Setup',
        priority: 'high',
        status: 'in-progress',
        assignedTo: 'Raj Kumar',
        createdDate: '2024-01-15',
        scheduledDate: '2024-01-20',
        completedDate: null,
        estimatedHours: 8,
        actualHours: 6,
        amount: 15000,
        location: 'Mumbai, Maharashtra'
      },
      {
        id: 2,
        serviceNumber: 'SRV-2024-002',
        customer: 'XYZ Trading Co.',
        customerId: 2,
        contactPerson: 'Jane Smith',
        type: 'Support',
        description: 'Data backup and recovery assistance',
        priority: 'medium',
        status: 'completed',
        assignedTo: 'Priya Sharma',
        createdDate: '2024-01-10',
        scheduledDate: '2024-01-12',
        completedDate: '2024-01-12',
        estimatedHours: 4,
        actualHours: 3,
        amount: 5000,
        location: 'Delhi, Delhi'
      },
      {
        id: 3,
        serviceNumber: 'SRV-2024-003',
        customer: 'PQR Industries',
        customerId: 3,
        contactPerson: 'Mike Johnson',
        type: 'Training',
        description: 'Advanced Tally features training for staff',
        priority: 'low',
        status: 'scheduled',
        assignedTo: 'Amit Singh',
        createdDate: '2024-01-08',
        scheduledDate: '2024-01-25',
        completedDate: null,
        estimatedHours: 16,
        actualHours: 0,
        amount: 8000,
        location: 'Bangalore, Karnataka'
      },
      {
        id: 4,
        serviceNumber: 'SRV-2024-004',
        customer: 'LMN Exports',
        customerId: 4,
        contactPerson: 'Sarah Wilson',
        type: 'Maintenance',
        description: 'Annual maintenance and software update',
        priority: 'medium',
        status: 'pending',
        assignedTo: 'Vikash Gupta',
        createdDate: '2024-01-18',
        scheduledDate: '2024-01-22',
        completedDate: null,
        estimatedHours: 6,
        actualHours: 0,
        amount: 12000,
        location: 'Chennai, Tamil Nadu'
      }
    ];
    
    setTimeout(() => {
      setServices(mockServices);
      setLoading(false);
    }, 1000);
  }, []);

  // Filter services based on search, status, and type
  const filteredServices = services.filter(service => {
    const matchesSearch = service.serviceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         service.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         service.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         service.assignedTo.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || service.status === filterStatus;
    const matchesType = filterType === 'all' || service.type === filterType;
    return matchesSearch && matchesStatus && matchesType;
  });

  // Pagination
  const indexOfLastService = currentPage * servicesPerPage;
  const indexOfFirstService = indexOfLastService - servicesPerPage;
  const currentServices = filteredServices.slice(indexOfFirstService, indexOfLastService);
  const totalPages = Math.ceil(filteredServices.length / servicesPerPage);

  const handleDelete = (serviceId) => {
    if (window.confirm('Are you sure you want to delete this service request?')) {
      setServices(services.filter(service => service.id !== serviceId));
      toast.success('Service request deleted successfully');
    }
  };

  const getStatusBadge = (status) => {
    const badgeClass = status === 'completed' ? 'bg-success' : 
                      status === 'in-progress' ? 'bg-info' : 
                      status === 'scheduled' ? 'bg-warning' : 
                      status === 'pending' ? 'bg-secondary' : 'bg-danger';
    return <span className={`badge ${badgeClass}`}>{status.toUpperCase().replace('-', ' ')}</span>;
  };

  const getPriorityBadge = (priority) => {
    const badgeClass = priority === 'high' ? 'bg-danger' : 
                      priority === 'medium' ? 'bg-warning' : 'bg-success';
    return <span className={`badge ${badgeClass}`}>{priority.toUpperCase()}</span>;
  };

  const getServiceTypeIcon = (type) => {
    switch (type) {
      case 'Installation': return <FaTools className="text-primary" />;
      case 'Support': return <FaUser className="text-info" />;
      case 'Training': return <FaUser className="text-success" />;
      case 'Maintenance': return <FaTools className="text-warning" />;
      default: return <FaTools className="text-secondary" />;
    }
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container-fluid">
      {/* Header */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h2 className="mb-0">Service Management</h2>
              <p className="text-muted">Manage service requests and track progress</p>
            </div>
            <div className="d-flex gap-2">
              <button className="btn btn-outline-primary">
                <FaDownload className="me-2" />
                Export
              </button>
              <Link to="/services/add" className="btn btn-primary">
                <FaPlus className="me-2" />
                New Service Request
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="row mb-4">
        <div className="col-md-3">
          <div className="card bg-primary text-white">
            <div className="card-body">
              <div className="d-flex justify-content-between">
                <div>
                  <h4 className="mb-0">{services.length}</h4>
                  <p className="mb-0">Total Services</p>
                </div>
                <FaTools size={24} />
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card bg-info text-white">
            <div className="card-body">
              <div className="d-flex justify-content-between">
                <div>
                  <h4 className="mb-0">{services.filter(s => s.status === 'in-progress').length}</h4>
                  <p className="mb-0">In Progress</p>
                </div>
                <FaClock size={24} />
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card bg-warning text-white">
            <div className="card-body">
              <div className="d-flex justify-content-between">
                <div>
                  <h4 className="mb-0">{services.filter(s => s.status === 'scheduled').length}</h4>
                  <p className="mb-0">Scheduled</p>
                </div>
                <FaCalendar size={24} />
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card bg-success text-white">
            <div className="card-body">
              <div className="d-flex justify-content-between">
                <div>
                  <h4 className="mb-0">{services.filter(s => s.status === 'completed').length}</h4>
                  <p className="mb-0">Completed</p>
                </div>
                <FaUser size={24} />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="row mb-4">
        <div className="col-md-4">
          <div className="input-group">
            <span className="input-group-text">
              <FaSearch />
            </span>
            <input
              type="text"
              className="form-control"
              placeholder="Search services..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
        <div className="col-md-2">
          <select
            className="form-select"
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
          >
            <option value="all">All Status</option>
            <option value="pending">Pending</option>
            <option value="scheduled">Scheduled</option>
            <option value="in-progress">In Progress</option>
            <option value="completed">Completed</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>
        <div className="col-md-2">
          <select
            className="form-select"
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
          >
            <option value="all">All Types</option>
            <option value="Installation">Installation</option>
            <option value="Support">Support</option>
            <option value="Training">Training</option>
            <option value="Maintenance">Maintenance</option>
          </select>
        </div>
        <div className="col-md-4">
          <button className="btn btn-outline-secondary w-100">
            <FaFilter className="me-2" />
            More Filters
          </button>
        </div>
      </div>

      {/* Service Table */}
      <div className="row">
        <div className="col-12">
          <div className="card">
            <div className="card-body">
              <div className="table-responsive">
                <table className="table table-hover">
                  <thead className="table-light">
                    <tr>
                      <th>Service #</th>
                      <th>Customer</th>
                      <th>Type</th>
                      <th>Description</th>
                      <th>Assigned To</th>
                      <th>Priority</th>
                      <th>Status</th>
                      <th>Scheduled Date</th>
                      <th>Amount</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {currentServices.map(service => (
                      <tr key={service.id}>
                        <td>
                          <strong>{service.serviceNumber}</strong>
                        </td>
                        <td>
                          <div>
                            <h6 className="mb-0">{service.customer}</h6>
                            <small className="text-muted">{service.contactPerson}</small>
                          </div>
                        </td>
                        <td>
                          <div className="d-flex align-items-center">
                            {getServiceTypeIcon(service.type)}
                            <span className="ms-2">{service.type}</span>
                          </div>
                        </td>
                        <td>
                          <span title={service.description}>
                            {service.description.length > 50 
                              ? `${service.description.substring(0, 50)}...` 
                              : service.description}
                          </span>
                        </td>
                        <td>{service.assignedTo}</td>
                        <td>{getPriorityBadge(service.priority)}</td>
                        <td>{getStatusBadge(service.status)}</td>
                        <td>
                          <div className="d-flex align-items-center">
                            <FaCalendar className="text-muted me-2" size={12} />
                            {new Date(service.scheduledDate).toLocaleDateString()}
                          </div>
                        </td>
                        <td>
                          <div className="d-flex align-items-center">
                            <FaRupeeSign className="text-muted me-1" size={12} />
                            <strong>{service.amount.toLocaleString()}</strong>
                          </div>
                        </td>
                        <td>
                          <div className="btn-group" role="group">
                            <button
                              className="btn btn-sm btn-outline-primary"
                              onClick={() => navigate(`/services/${service.id}`)}
                              title="View Details"
                            >
                              <FaEye />
                            </button>
                            <button
                              className="btn btn-sm btn-outline-secondary"
                              onClick={() => navigate(`/services/${service.id}/edit`)}
                              title="Edit"
                            >
                              <FaEdit />
                            </button>
                            <button
                              className="btn btn-sm btn-outline-danger"
                              onClick={() => handleDelete(service.id)}
                              title="Delete"
                            >
                              <FaTrash />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <nav className="mt-4">
                  <ul className="pagination justify-content-center">
                    <li className={`page-item ${currentPage === 1 ? 'disabled' : ''}`}>
                      <button
                        className="page-link"
                        onClick={() => setCurrentPage(currentPage - 1)}
                        disabled={currentPage === 1}
                      >
                        Previous
                      </button>
                    </li>
                    {[...Array(totalPages)].map((_, index) => (
                      <li key={index} className={`page-item ${currentPage === index + 1 ? 'active' : ''}`}>
                        <button
                          className="page-link"
                          onClick={() => setCurrentPage(index + 1)}
                        >
                          {index + 1}
                        </button>
                      </li>
                    ))}
                    <li className={`page-item ${currentPage === totalPages ? 'disabled' : ''}`}>
                      <button
                        className="page-link"
                        onClick={() => setCurrentPage(currentPage + 1)}
                        disabled={currentPage === totalPages}
                      >
                        Next
                      </button>
                    </li>
                  </ul>
                </nav>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ServiceList;
