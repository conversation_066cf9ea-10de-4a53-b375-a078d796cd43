import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const ServiceCall = sequelize.define('ServiceCall', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
    },
    call_number: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      comment: 'Auto-generated service call number',
    },
    customer_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'customers',
        key: 'id',
      },
    },
    contact_person_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'customer_contacts',
        key: 'id',
      },
    },
    tss_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'customer_tss',
        key: 'id',
      },
    },
    amc_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'customer_amc',
        key: 'id',
      },
    },
    call_type: {
      type: DataTypes.ENUM('online', 'onsite', 'phone', 'email'),
      allowNull: false,
      defaultValue: 'online',
    },
    priority: {
      type: DataTypes.ENUM('low', 'medium', 'high', 'critical'),
      allowNull: false,
      defaultValue: 'medium',
    },
    nature_of_issue_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'nature_of_issues',
        key: 'id',
      },
    },
    status_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'call_statuses',
        key: 'id',
      },
    },
    area_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'areas',
        key: 'id',
      },
    },
    assigned_to: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'executives',
        key: 'id',
      },
    },
    created_by: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    subject: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [5, 200],
      },
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    customer_reported_issue: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Issue as reported by customer',
    },
    actual_issue_found: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Actual issue found during investigation',
    },
    solution_provided: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Solution provided to resolve the issue',
    },
    call_date: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    scheduled_date: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'Scheduled date for onsite visits',
    },
    started_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'When work on the call started',
    },
    completed_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'When the call was completed',
    },
    closed_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'When the call was closed',
    },
    estimated_hours: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
      comment: 'Estimated hours to resolve',
    },
    actual_hours: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
      comment: 'Actual hours spent',
    },
    billable_hours: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
      comment: 'Billable hours',
    },
    hourly_rate: {
      type: DataTypes.DECIMAL(8, 2),
      allowNull: true,
      comment: 'Hourly rate for billing',
    },
    service_charges: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
      comment: 'Service charges amount',
    },
    travel_charges: {
      type: DataTypes.DECIMAL(8, 2),
      allowNull: true,
      defaultValue: 0.00,
      comment: 'Travel charges for onsite visits',
    },
    total_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
      comment: 'Total billable amount',
    },
    is_billable: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      comment: 'Whether this call is billable',
    },
    is_under_amc: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Whether this call is covered under AMC',
    },
    customer_satisfaction: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 1,
        max: 5,
      },
      comment: 'Customer satisfaction rating (1-5)',
    },
    customer_feedback: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Customer feedback',
    },
    internal_notes: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Internal notes not visible to customer',
    },
    follow_up_required: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    follow_up_date: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    follow_up_notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    attachments: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Array of attachment file paths',
    },
    tags: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Array of tags for categorization',
    },
    custom_fields: {
      type: DataTypes.JSONB,
      defaultValue: {},
      comment: 'Custom fields for additional data',
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'service_calls',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['tenant_id'],
      },
      {
        fields: ['call_number'],
        unique: true,
      },
      {
        fields: ['customer_id'],
      },
      {
        fields: ['call_type'],
      },
      {
        fields: ['priority'],
      },
      {
        fields: ['status_id'],
      },
      {
        fields: ['assigned_to'],
      },
      {
        fields: ['created_by'],
      },
      {
        fields: ['call_date'],
      },
      {
        fields: ['scheduled_date'],
      },
      {
        fields: ['is_billable'],
      },
      {
        fields: ['is_under_amc'],
      },
      {
        fields: ['follow_up_required'],
      },
      {
        fields: ['follow_up_date'],
      },
    ],
  });

  // Instance methods
  ServiceCall.prototype.getDuration = function() {
    if (!this.started_at || !this.completed_at) return null;
    const diffTime = new Date(this.completed_at) - new Date(this.started_at);
    const diffHours = diffTime / (1000 * 60 * 60);
    return Math.round(diffHours * 100) / 100;
  };

  ServiceCall.prototype.isOverdue = function() {
    if (!this.scheduled_date) return false;
    return new Date(this.scheduled_date) < new Date() && !this.completed_at;
  };

  ServiceCall.prototype.getResponseTime = function() {
    if (!this.started_at) return null;
    const diffTime = new Date(this.started_at) - new Date(this.call_date);
    const diffHours = diffTime / (1000 * 60 * 60);
    return Math.round(diffHours * 100) / 100;
  };

  ServiceCall.prototype.getResolutionTime = function() {
    if (!this.completed_at) return null;
    const diffTime = new Date(this.completed_at) - new Date(this.call_date);
    const diffHours = diffTime / (1000 * 60 * 60);
    return Math.round(diffHours * 100) / 100;
  };

  ServiceCall.prototype.getPriorityColor = function() {
    const colors = {
      low: '#28a745',
      medium: '#ffc107',
      high: '#fd7e14',
      critical: '#dc3545',
    };
    return colors[this.priority] || '#6c757d';
  };

  ServiceCall.prototype.calculateTotalAmount = function() {
    let total = 0;
    if (this.service_charges) total += parseFloat(this.service_charges);
    if (this.travel_charges) total += parseFloat(this.travel_charges);
    if (this.billable_hours && this.hourly_rate) {
      total += parseFloat(this.billable_hours) * parseFloat(this.hourly_rate);
    }
    return Math.round(total * 100) / 100;
  };

  // Associations
  ServiceCall.associate = function(models) {
    ServiceCall.belongsTo(models.Tenant, {
      foreignKey: 'tenant_id',
      as: 'tenant',
    });

    ServiceCall.belongsTo(models.Customer, {
      foreignKey: 'customer_id',
      as: 'customer',
    });

    ServiceCall.belongsTo(models.CustomerContact, {
      foreignKey: 'contact_person_id',
      as: 'contactPerson',
    });

    ServiceCall.belongsTo(models.CustomerTSS, {
      foreignKey: 'tss_id',
      as: 'tss',
    });

    ServiceCall.belongsTo(models.CustomerAMC, {
      foreignKey: 'amc_id',
      as: 'amc',
    });

    ServiceCall.belongsTo(models.NatureOfIssue, {
      foreignKey: 'nature_of_issue_id',
      as: 'natureOfIssue',
    });

    ServiceCall.belongsTo(models.CallStatus, {
      foreignKey: 'status_id',
      as: 'status',
    });

    ServiceCall.belongsTo(models.Area, {
      foreignKey: 'area_id',
      as: 'area',
    });

    ServiceCall.belongsTo(models.Executive, {
      foreignKey: 'assigned_to',
      as: 'assignedExecutive',
    });

    ServiceCall.belongsTo(models.User, {
      foreignKey: 'created_by',
      as: 'creator',
    });

    ServiceCall.hasMany(models.ServiceCallItem, {
      foreignKey: 'service_call_id',
      as: 'items',
    });
  };

  return ServiceCall;
}
