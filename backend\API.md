# TallyCRM API Documentation

## Overview

TallyCRM provides a comprehensive REST API for managing customers, service calls, sales, and other CRM operations for Tally resellers. The API follows RESTful conventions and returns JSON responses.

## Base URL

```
http://localhost:3000/api/v1
```

## Authentication

All API endpoints (except authentication endpoints) require a valid JWT token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Response Format

All API responses follow this standard format:

```json
{
  "success": true|false,
  "message": "Response message",
  "data": {
    // Response data
  },
  "errors": {
    // Validation errors (if any)
  }
}
```

## Pagination

List endpoints support pagination with these query parameters:

- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10, max: 100)
- `sortBy`: Field to sort by
- `sortOrder`: ASC or DESC (default: DESC)

Pagination response includes:

```json
{
  "data": {
    "items": [...],
    "pagination": {
      "currentPage": 1,
      "totalPages": 5,
      "totalItems": 50,
      "itemsPerPage": 10
    }
  }
}
```

## Error Handling

HTTP Status Codes:
- `200`: Success
- `201`: Created
- `400`: Bad Request (validation errors)
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `409`: Conflict (duplicate data)
- `500`: Internal Server Error

## API Endpoints

### Authentication

#### Register
```
POST /auth/register
```

Register a new user and tenant.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123",
  "firstName": "John",
  "lastName": "Doe",
  "phone": "+91-**********",
  "tenantName": "My Company",
  "tenantSlug": "my-company"
}
```

#### Login
```
POST /auth/login
```

Authenticate user and get JWT token.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "token": "jwt-token-here",
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe"
    },
    "tenant": {
      "id": "uuid",
      "name": "My Company",
      "subscriptionPlan": "trial"
    }
  }
}
```

#### Get Profile
```
GET /auth/profile
```

Get current user profile information.

### Customers

#### List Customers
```
GET /customers
```

**Query Parameters:**
- `page`, `limit`, `sortBy`, `sortOrder` (pagination)
- `search`: Search in company name, code, email, phone
- `customerType`: prospect, customer, inactive, blacklisted
- `industryId`: Filter by industry UUID
- `areaId`: Filter by area UUID
- `assignedExecutiveId`: Filter by assigned executive UUID
- `isActive`: true/false

#### Get Customer
```
GET /customers/:id
```

#### Create Customer
```
POST /customers
```

**Request Body:**
```json
{
  "company_name": "ABC Enterprises",
  "customer_code": "CUST001",
  "customer_type": "prospect",
  "business_type": "private_limited",
  "industry_id": "uuid",
  "area_id": "uuid",
  "email": "<EMAIL>",
  "phone": "+91-**********",
  "website": "https://abc.com",
  "gst_number": "27**********1Z5",
  "pan_number": "**********",
  "annual_turnover": 5000000.00,
  "employee_count": 50,
  "assigned_executive_id": "uuid"
}
```

#### Update Customer
```
PUT /customers/:id
```

#### Delete Customer
```
DELETE /customers/:id
```

#### Customer Statistics
```
GET /customers/stats
```

### Service Calls

#### List Service Calls
```
GET /service-calls
```

**Query Parameters:**
- `page`, `limit`, `sortBy`, `sortOrder` (pagination)
- `search`: Search in call number, subject, description
- `customerId`: Filter by customer UUID
- `statusId`: Filter by status UUID
- `assignedTo`: Filter by assigned executive UUID
- `callType`: online, onsite, phone, email
- `priority`: low, medium, high, critical
- `isUnderAmc`: true/false
- `dateFrom`, `dateTo`: Date range filter

#### Get Service Call
```
GET /service-calls/:id
```

#### Create Service Call
```
POST /service-calls
```

**Request Body:**
```json
{
  "customer_id": "uuid",
  "subject": "Tally installation issue",
  "description": "Customer facing issues with Tally installation",
  "contact_person_id": "uuid",
  "call_type": "onsite",
  "priority": "high",
  "nature_of_issue_id": "uuid",
  "assigned_to": "uuid",
  "scheduled_date": "2024-01-15T10:00:00Z",
  "estimated_hours": 2.5,
  "is_billable": true,
  "items": [
    {
      "item_type": "service",
      "description": "Installation service",
      "quantity": 1,
      "rate": 1000.00
    }
  ]
}
```

#### Update Service Call
```
PUT /service-calls/:id
```

#### Service Call Statistics
```
GET /service-calls/stats
```

### Executives

#### List Executives
```
GET /executives
```

**Query Parameters:**
- `page`, `limit`, `sortBy`, `sortOrder` (pagination)
- `search`: Search in name, employee code, email, phone
- `department`: sales, technical, support, management, accounts, hr, marketing
- `isActive`: true/false

#### Get Executive
```
GET /executives/:id
```

#### Create Executive
```
POST /executives
```

**Request Body:**
```json
{
  "first_name": "John",
  "last_name": "Smith",
  "employee_code": "EMP001",
  "email": "<EMAIL>",
  "phone": "+91-**********",
  "designation_id": "uuid",
  "staff_role_id": "uuid",
  "department": "sales",
  "date_of_joining": "2024-01-01",
  "salary": 50000.00,
  "commission_rate": 5.00,
  "target_amount": 1000000.00,
  "skills": ["Tally", "Accounting", "Sales"],
  "areas_covered": ["uuid1", "uuid2"]
}
```

#### Update Executive
```
PUT /executives/:id
```

#### Delete Executive
```
DELETE /executives/:id
```

#### Executive Statistics
```
GET /executives/stats
```

### Master Data

All master data endpoints follow the same pattern:

#### License Editions
```
GET /master-data/license-editions
POST /master-data/license-editions
GET /master-data/license-editions/:id
PUT /master-data/license-editions/:id
DELETE /master-data/license-editions/:id
PATCH /master-data/license-editions/:id/toggle-active
```

#### Other Master Data Endpoints
- `/master-data/designations`
- `/master-data/tally-products`
- `/master-data/staff-roles`
- `/master-data/industries`
- `/master-data/areas`
- `/master-data/nature-of-issues`
- `/master-data/additional-services`
- `/master-data/call-statuses`

### Dashboard

#### Dashboard Overview
```
GET /dashboard/overview
```

Returns comprehensive dashboard statistics including:
- Summary counts
- Monthly activity
- Growth percentages
- Revenue statistics
- Recent activity
- Alerts (follow-ups, expiring AMCs)

#### Dashboard Charts
```
GET /dashboard/charts?period=30d
```

**Query Parameters:**
- `period`: 7d, 30d, 3m, 1y

Returns trend data for charts:
- Service calls trend
- Sales trend
- Customer acquisition trend

## Permissions

The API uses role-based access control. Common permissions include:

- `dashboard.read`
- `customers.read`, `customers.create`, `customers.update`, `customers.delete`
- `service_calls.read`, `service_calls.create`, `service_calls.update`, `service_calls.delete`
- `executives.read`, `executives.create`, `executives.update`, `executives.delete`
- `master_data.read`, `master_data.create`, `master_data.update`, `master_data.delete`

## Rate Limiting

API requests are rate-limited to prevent abuse:
- 100 requests per 15 minutes per IP address
- Higher limits available for authenticated users

## Webhooks (Future)

Webhook endpoints will be available for real-time notifications:
- Customer created/updated
- Service call status changed
- Payment received
- AMC expiring

## SDKs and Libraries

Official SDKs will be available for:
- JavaScript/Node.js
- PHP
- Python
- .NET

## Support

For API support:
- Email: <EMAIL>
- Documentation: https://docs.tallycrm.com
- Status Page: https://status.tallycrm.com
