import sqlite3 from 'sqlite3';
import { logger } from './src/utils/logger.js';
import fs from 'fs';
import path from 'path';

const checkDatabase = async () => {
  const dbPath = './database/tallycrm_dev.sqlite';

  // Check if database file exists
  if (fs.existsSync(dbPath)) {
    const stats = fs.statSync(dbPath);
    logger.info(`📁 Database file exists: ${dbPath} (${stats.size} bytes)`);
  } else {
    logger.error(`❌ Database file not found: ${dbPath}`);
    return;
  }

  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database(dbPath, (err) => {
      if (err) {
        logger.error('❌ Error opening database:', err);
        reject(err);
        return;
      }

      logger.info('✅ Database opened successfully');

      // Get all tables
      db.all("SELECT name FROM sqlite_master WHERE type='table'", (err, rows) => {
        if (err) {
          logger.error('❌ Error getting tables:', err);
          reject(err);
          return;
        }

        logger.info('📋 Tables in database:', rows.map(row => row.name));

        // Check migrations table specifically
        db.all("SELECT * FROM migrations ORDER BY id", (err, migrations) => {
          if (err) {
            logger.error('❌ Error getting migrations:', err);
          } else {
            logger.info('📋 Executed migrations:', migrations.map(m => m.name));
          }

          db.close((err) => {
            if (err) {
              logger.error('❌ Error closing database:', err);
              reject(err);
            } else {
              logger.info('✅ Database closed');
              resolve();
            }
          });
        });
      });
    });
  });
};

checkDatabase().catch(console.error);
