import { describe, it, expect, vi } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from 'react-query';
import { HelmetProvider } from 'react-helmet-async';
import App from './App';

// Mock the useAuth hook
vi.mock('./hooks/useAuth', () => ({
  useAuth: () => ({
    isAuthenticated: false,
    isLoading: false,
    user: null,
  }),
}));

const renderWithProviders = (component) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return render(
    <HelmetProvider>
      <QueryClientProvider client={queryClient}>
        <BrowserRouter>
          {component}
        </BrowserRouter>
      </QueryClientProvider>
    </HelmetProvider>
  );
};

describe('App', () => {
  it('renders without crashing', async () => {
    renderWithProviders(<App />);
    // The Login page sets its own title "Login - TallyCRM"
    await waitFor(() => {
      expect(document.title).toBe('Login - TallyCRM');
    });
  });
  
  it('shows login page when not authenticated', () => {
    const { container } = renderWithProviders(<App />);
    // Since we're mocking useAuth to return isAuthenticated: false,
    // the app should redirect to auth/login and show the login form
    expect(container.querySelector('.auth-layout')).toBeTruthy();
  });
});
