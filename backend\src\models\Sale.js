import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const Sale = sequelize.define('Sale', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    tenant_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'tenants',
        key: 'id',
      },
    },
    sale_number: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      comment: 'Auto-generated sale number',
    },
    customer_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'customers',
        key: 'id',
      },
    },
    sales_executive_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'executives',
        key: 'id',
      },
    },
    sale_type: {
      type: DataTypes.ENUM('new', 'renewal', 'upgrade', 'additional'),
      allowNull: false,
      defaultValue: 'new',
    },
    sale_date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    quotation_number: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Reference quotation number',
    },
    quotation_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    po_number: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Customer purchase order number',
    },
    po_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    delivery_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    installation_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    status: {
      type: DataTypes.ENUM('draft', 'confirmed', 'delivered', 'installed', 'completed', 'cancelled'),
      allowNull: false,
      defaultValue: 'draft',
    },
    payment_terms: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: 'Immediate',
    },
    payment_status: {
      type: DataTypes.ENUM('pending', 'partial', 'paid', 'overdue'),
      allowNull: false,
      defaultValue: 'pending',
    },
    subtotal: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 0.00,
    },
    discount_percentage: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    discount_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    taxable_amount: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 0.00,
    },
    cgst_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    sgst_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    igst_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    total_tax_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
    },
    total_amount: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 0.00,
    },
    paid_amount: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 0.00,
    },
    balance_amount: {
      type: DataTypes.DECIMAL(12, 2),
      allowNull: false,
      defaultValue: 0.00,
    },
    commission_percentage: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    commission_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    lead_source: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Source of the lead that resulted in this sale',
    },
    referral_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'referrals',
        key: 'id',
      },
    },
    billing_address: {
      type: DataTypes.JSONB,
      allowNull: true,
      comment: 'Billing address details',
    },
    shipping_address: {
      type: DataTypes.JSONB,
      allowNull: true,
      comment: 'Shipping address details',
    },
    terms_and_conditions: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    internal_notes: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Internal notes not visible to customer',
    },
    attachments: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Array of attachment file paths',
    },
    tags: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Array of tags for categorization',
    },
    custom_fields: {
      type: DataTypes.JSONB,
      defaultValue: {},
      comment: 'Custom fields for additional data',
    },
    created_by: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    approved_by: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    approved_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'sales',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['tenant_id'],
      },
      {
        fields: ['sale_number'],
        unique: true,
      },
      {
        fields: ['customer_id'],
      },
      {
        fields: ['sales_executive_id'],
      },
      {
        fields: ['sale_type'],
      },
      {
        fields: ['sale_date'],
      },
      {
        fields: ['status'],
      },
      {
        fields: ['payment_status'],
      },
      {
        fields: ['created_by'],
      },
      {
        fields: ['approved_by'],
      },
    ],
  });

  // Instance methods
  Sale.prototype.calculateSubtotal = function() {
    // This would sum up all sale items
    // Implementation would require sale items to be loaded
    return parseFloat(this.subtotal);
  };

  Sale.prototype.calculateDiscountAmount = function() {
    const subtotal = this.calculateSubtotal();
    if (this.discount_percentage > 0) {
      return (subtotal * parseFloat(this.discount_percentage)) / 100;
    }
    return parseFloat(this.discount_amount) || 0;
  };

  Sale.prototype.calculateTaxableAmount = function() {
    const subtotal = this.calculateSubtotal();
    const discountAmount = this.calculateDiscountAmount();
    return subtotal - discountAmount;
  };

  Sale.prototype.calculateTotalTaxAmount = function() {
    return parseFloat(this.cgst_amount || 0) + 
           parseFloat(this.sgst_amount || 0) + 
           parseFloat(this.igst_amount || 0);
  };

  Sale.prototype.calculateTotalAmount = function() {
    const taxableAmount = this.calculateTaxableAmount();
    const totalTax = this.calculateTotalTaxAmount();
    return taxableAmount + totalTax;
  };

  Sale.prototype.calculateBalanceAmount = function() {
    const totalAmount = this.calculateTotalAmount();
    const paidAmount = parseFloat(this.paid_amount || 0);
    return totalAmount - paidAmount;
  };

  Sale.prototype.calculateCommissionAmount = function() {
    if (this.commission_percentage > 0) {
      const totalAmount = this.calculateTotalAmount();
      return (totalAmount * parseFloat(this.commission_percentage)) / 100;
    }
    return parseFloat(this.commission_amount) || 0;
  };

  Sale.prototype.isOverdue = function() {
    if (this.payment_status === 'paid') return false;
    if (!this.delivery_date) return false;
    
    // Consider overdue if payment is pending 30 days after delivery
    const overdueDate = new Date(this.delivery_date);
    overdueDate.setDate(overdueDate.getDate() + 30);
    return new Date() > overdueDate;
  };

  Sale.prototype.getStatusColor = function() {
    const colors = {
      draft: '#6c757d',
      confirmed: '#17a2b8',
      delivered: '#ffc107',
      installed: '#fd7e14',
      completed: '#28a745',
      cancelled: '#dc3545',
    };
    return colors[this.status] || '#6c757d';
  };

  Sale.prototype.getPaymentStatusColor = function() {
    const colors = {
      pending: '#ffc107',
      partial: '#fd7e14',
      paid: '#28a745',
      overdue: '#dc3545',
    };
    return colors[this.payment_status] || '#6c757d';
  };

  // Associations
  Sale.associate = function(models) {
    Sale.belongsTo(models.Tenant, {
      foreignKey: 'tenant_id',
      as: 'tenant',
    });

    Sale.belongsTo(models.Customer, {
      foreignKey: 'customer_id',
      as: 'customer',
    });

    Sale.belongsTo(models.Executive, {
      foreignKey: 'sales_executive_id',
      as: 'salesExecutive',
    });

    // TODO: Create Referral model
    // Sale.belongsTo(models.Referral, {
    //   foreignKey: 'referral_id',
    //   as: 'referral',
    // });

    Sale.belongsTo(models.User, {
      foreignKey: 'created_by',
      as: 'creator',
    });

    Sale.belongsTo(models.User, {
      foreignKey: 'approved_by',
      as: 'approver',
    });

    Sale.hasMany(models.SaleItem, {
      foreignKey: 'sale_id',
      as: 'items',
    });
  };

  return Sale;
}
