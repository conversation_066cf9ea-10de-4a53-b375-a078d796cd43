import React, { useState, useEffect } from 'react';
import { Contain<PERSON>, Row, Col, Card, <PERSON>, Badge, Spinner, Alert } from 'react-bootstrap';
import { Helmet } from 'react-helmet-async';
import { apiService } from '../services/api';

const Dashboard = () => {
  const [stats, setStats] = useState({
    totalCustomers: 0,
    activeServices: 0,
    monthlyRevenue: 0,
    pendingTasks: 0,
    totalSales: 0,
    activeLicenses: 0,
    expiringAMCs: 0,
    openServiceCalls: 0
  });
  const [recentCustomers, setRecentCustomers] = useState([]);
  const [recentServiceCalls, setRecentServiceCalls] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch dashboard data from real API endpoints
      const [dashboardRes, customersRes, serviceCallsRes] = await Promise.all([
        apiService.get('/dashboard/overview').catch(() => ({ data: { data: null } })),
        apiService.get('/customers?limit=5').catch(() => ({ data: { data: { customers: [] } } })),
        apiService.get('/service-calls?limit=5').catch(() => ({ data: { data: { serviceCalls: [] } } }))
      ]);

      // Use dashboard overview data if available
      const dashboardData = dashboardRes.data?.data;
      if (dashboardData) {
        setStats({
          totalCustomers: dashboardData.summary?.totalCustomers || 0,
          activeServices: dashboardData.summary?.totalServiceCalls || 0,
          monthlyRevenue: dashboardData.revenue?.salesTotal || 0,
          pendingTasks: dashboardData.summary?.openServiceCalls || 0,
          totalSales: dashboardData.summary?.totalSales || 0,
          activeLicenses: dashboardData.summary?.activeCustomers || 0,
          expiringAMCs: dashboardData.alerts?.expiringAMCs?.length || 0,
          openServiceCalls: dashboardData.summary?.openServiceCalls || 0
        });

        // Set recent data from dashboard
        setRecentServiceCalls(dashboardData.recentActivity?.recentServiceCalls || []);
      } else {
        // Fallback to individual API calls
        const totalCustomers = customersRes.data?.data?.pagination?.total || 0;
        const totalServiceCalls = serviceCallsRes.data?.data?.pagination?.total || 0;
        const openServiceCalls = serviceCallsRes.data?.data?.serviceCalls?.filter(sc => sc.status === 'open')?.length || 0;

        setStats({
          totalCustomers,
          activeServices: totalServiceCalls,
          monthlyRevenue: 0,
          pendingTasks: openServiceCalls,
          totalSales: 0,
          activeLicenses: totalCustomers,
          expiringAMCs: 0,
          openServiceCalls
        });
      }

      // Set recent data
      setRecentCustomers(customersRes.data?.data?.customers || []);
      setRecentServiceCalls(serviceCallsRes.data?.data?.serviceCalls || []);

    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
      setError('Failed to load dashboard data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <Container fluid className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
      </Container>
    );
  }

  if (error) {
    return (
      <Container fluid>
        <Alert variant="danger" className="mt-4">
          <Alert.Heading>Error Loading Dashboard</Alert.Heading>
          <p>{error}</p>
          <button className="btn btn-outline-danger" onClick={fetchDashboardData}>
            Try Again
          </button>
        </Alert>
      </Container>
    );
  }

  const statsCards = [
    {
      title: 'Total Customers',
      value: stats.totalCustomers.toLocaleString(),
      icon: 'bi-people',
      color: 'primary'
    },
    {
      title: 'Active Services',
      value: stats.activeServices.toLocaleString(),
      icon: 'bi-tools',
      color: 'success'
    },
    {
      title: 'Monthly Revenue',
      value: formatCurrency(stats.monthlyRevenue),
      icon: 'bi-currency-rupee',
      color: 'info'
    },
    {
      title: 'Open Service Calls',
      value: stats.openServiceCalls.toLocaleString(),
      icon: 'bi-headset',
      color: 'warning'
    }
  ];



  return (
    <>
      <Helmet>
        <title>Dashboard - TallyCRM</title>
      </Helmet>

      <Container fluid>
        {/* Page Header */}
        <Row className="mb-4">
          <Col>
            <h1 className="h3 mb-0">Dashboard</h1>
            <p className="text-muted">Welcome back! Here's what's happening with your business today.</p>
          </Col>
        </Row>

        {/* Stats Cards */}
        <Row className="mb-4">
          {statsCards.map((stat, index) => (
            <Col key={index} sm={6} lg={3} className="mb-3">
              <Card className="h-100 border-0 shadow-sm">
                <Card.Body>
                  <div className="d-flex align-items-center">
                    <div className={`rounded-circle bg-${stat.color} bg-opacity-10 p-3 me-3`}>
                      <i className={`bi ${stat.icon} text-${stat.color} fs-4`}></i>
                    </div>
                    <div className="flex-grow-1">
                      <h3 className="mb-0">{stat.value}</h3>
                      <p className="text-muted mb-1">{stat.title}</p>
                      <small className="text-muted">Real-time data</small>
                    </div>
                  </div>
                </Card.Body>
              </Card>
            </Col>
          ))}
        </Row>

        <Row>
          {/* Recent Customers */}
          <Col lg={6} className="mb-4">
            <Card className="h-100 border-0 shadow-sm">
              <Card.Header className="bg-white border-bottom">
                <h5 className="mb-0">Recent Customers</h5>
              </Card.Header>
              <Card.Body>
                {recentCustomers.length > 0 ? (
                  <Table responsive hover>
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Phone</th>
                        <th>Created</th>
                      </tr>
                    </thead>
                    <tbody>
                      {recentCustomers.map((customer) => (
                        <tr key={customer.id}>
                          <td>{customer.company_name || `${customer.first_name} ${customer.last_name}`}</td>
                          <td>{customer.email}</td>
                          <td>{customer.phone}</td>
                          <td>{formatDate(customer.created_at)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </Table>
                ) : (
                  <div className="text-center py-4">
                    <i className="bi bi-people fs-1 text-muted"></i>
                    <p className="text-muted mt-2">No customers found</p>
                  </div>
                )}
              </Card.Body>
            </Card>
          </Col>

          {/* Recent Service Calls */}
          <Col lg={6} className="mb-4">
            <Card className="h-100 border-0 shadow-sm">
              <Card.Header className="bg-white border-bottom">
                <h5 className="mb-0">Recent Service Calls</h5>
              </Card.Header>
              <Card.Body>
                {recentServiceCalls.length > 0 ? (
                  <Table responsive hover>
                    <thead>
                      <tr>
                        <th>Title</th>
                        <th>Status</th>
                        <th>Priority</th>
                        <th>Created</th>
                      </tr>
                    </thead>
                    <tbody>
                      {recentServiceCalls.map((serviceCall) => (
                        <tr key={serviceCall.id}>
                          <td>{serviceCall.subject || serviceCall.title || 'N/A'}</td>
                          <td>
                            <Badge bg={
                              serviceCall.status?.category === 'open' || serviceCall.status === 'open' ? 'warning' :
                              serviceCall.status?.category === 'in_progress' || serviceCall.status === 'in_progress' ? 'info' :
                              serviceCall.status?.category === 'resolved' || serviceCall.status === 'resolved' ? 'success' : 'secondary'
                            }>
                              {serviceCall.status?.name || serviceCall.status?.replace('_', ' ').toUpperCase() || 'UNKNOWN'}
                            </Badge>
                          </td>
                          <td>
                            <Badge bg={
                              serviceCall.priority === 'high' ? 'danger' :
                              serviceCall.priority === 'medium' ? 'warning' : 'secondary'
                            }>
                              {serviceCall.priority?.toUpperCase() || 'LOW'}
                            </Badge>
                          </td>
                          <td>{formatDate(serviceCall.createdAt || serviceCall.created_at)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </Table>
                ) : (
                  <div className="text-center py-4">
                    <i className="bi bi-headset fs-1 text-muted"></i>
                    <p className="text-muted mt-2">No service calls found</p>
                  </div>
                )}
              </Card.Body>
            </Card>
          </Col>
        </Row>

        <Row>
          {/* Quick Actions */}
          <Col lg={12} className="mb-4">
            <Card className="h-100 border-0 shadow-sm">
              <Card.Header className="bg-white border-bottom">
                <h5 className="mb-0">Quick Actions</h5>
              </Card.Header>
              <Card.Body>
                <div className="d-grid gap-2">
                  <button className="btn btn-primary">
                    <i className="bi bi-person-plus me-2"></i>
                    Add New Customer
                  </button>
                  <button className="btn btn-outline-primary">
                    <i className="bi bi-tools me-2"></i>
                    Create Service Call
                  </button>
                  <button className="btn btn-outline-primary">
                    <i className="bi bi-graph-up me-2"></i>
                    Record Sale
                  </button>
                  <button className="btn btn-outline-primary">
                    <i className="bi bi-file-earmark-text me-2"></i>
                    Generate Report
                  </button>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default Dashboard;
