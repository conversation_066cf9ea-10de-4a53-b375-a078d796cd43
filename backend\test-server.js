import { logger } from './src/utils/logger.js';

const testServer = async () => {
  try {
    logger.info('Testing server imports...');
    
    // Test basic imports
    const express = await import('express');
    logger.info('✅ Express imported');
    
    const cors = await import('cors');
    logger.info('✅ CORS imported');
    
    const helmet = await import('helmet');
    logger.info('✅ Helmet imported');
    
    // Test config import
    const appConfig = await import('./config/app.js');
    logger.info('✅ App config imported');
    
    // Test database connection
    const { testConnection } = await import('./src/utils/database.js');
    const isConnected = await testConnection();
    logger.info(`✅ Database connection: ${isConnected ? 'OK' : 'FAILED'}`);
    
    // Test models
    const models = await import('./src/models/index.js');
    logger.info('✅ Models imported');
    
    // Test routes
    const healthRoutes = await import('./src/routes/health.js');
    logger.info('✅ Health routes imported');
    
    const authRoutes = await import('./src/routes/auth.js');
    logger.info('✅ Auth routes imported');
    
    logger.info('✅ All server components imported successfully!');
    
  } catch (error) {
    logger.error('❌ Server test failed:', error);
  }
};

testServer();
