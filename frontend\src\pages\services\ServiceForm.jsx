import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { FaSave, FaTimes, FaUser, FaTools, FaCalendar, FaRupeeSign } from 'react-icons/fa';

const ServiceForm = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEdit = Boolean(id);
  
  const [loading, setLoading] = useState(false);
  const [customers, setCustomers] = useState([]);
  const [technicians, setTechnicians] = useState([]);
  const [formData, setFormData] = useState({
    // Service Information
    serviceNumber: '',
    customerId: '',
    type: '',
    category: '',
    description: '',
    priority: 'medium',
    status: 'pending',
    
    // Assignment
    assignedTo: '',
    
    // Scheduling
    scheduledDate: '',
    scheduledTime: '',
    estimatedHours: '',
    
    // Location
    serviceLocation: 'customer-site',
    address: '',
    city: '',
    state: '',
    
    // Financial
    amount: '',
    currency: 'INR',
    paymentStatus: 'pending',
    
    // Additional Details
    requirements: '',
    notes: '',
    
    // Customer Contact
    contactPerson: '',
    contactPhone: '',
    contactEmail: ''
  });

  const [errors, setErrors] = useState({});

  // Mock data for dropdowns
  useEffect(() => {
    const mockCustomers = [
      { id: 1, name: 'ABC Enterprises', contactPerson: 'John Doe' },
      { id: 2, name: 'XYZ Trading Co.', contactPerson: 'Jane Smith' },
      { id: 3, name: 'PQR Industries', contactPerson: 'Mike Johnson' }
    ];
    
    const mockTechnicians = [
      { id: 1, name: 'Raj Kumar', specialization: 'Installation' },
      { id: 2, name: 'Priya Sharma', specialization: 'Support' },
      { id: 3, name: 'Amit Singh', specialization: 'Training' },
      { id: 4, name: 'Vikash Gupta', specialization: 'Maintenance' }
    ];
    
    setCustomers(mockCustomers);
    setTechnicians(mockTechnicians);
    
    // Generate service number for new services
    if (!isEdit) {
      const serviceNumber = `SRV-${new Date().getFullYear()}-${String(Date.now()).slice(-3)}`;
      setFormData(prev => ({ ...prev, serviceNumber }));
    }
  }, [isEdit]);

  // Mock data for edit mode
  useEffect(() => {
    if (isEdit) {
      setLoading(true);
      // Simulate API call
      setTimeout(() => {
        setFormData({
          serviceNumber: 'SRV-2024-001',
          customerId: '1',
          type: 'Installation',
          category: 'Software',
          description: 'Tally Prime Installation and Setup',
          priority: 'high',
          status: 'in-progress',
          assignedTo: '1',
          scheduledDate: '2024-01-20',
          scheduledTime: '10:00',
          estimatedHours: '8',
          serviceLocation: 'customer-site',
          address: '123 Business Park, Sector 5',
          city: 'Mumbai',
          state: 'Maharashtra',
          amount: '15000',
          currency: 'INR',
          paymentStatus: 'pending',
          requirements: 'Latest version of Tally Prime with multi-user license',
          notes: 'Customer prefers morning slot',
          contactPerson: 'John Doe',
          contactPhone: '+91 **********',
          contactEmail: '<EMAIL>'
        });
        setLoading(false);
      }, 1000);
    }
  }, [isEdit]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
    
    // Auto-fill customer details when customer is selected
    if (name === 'customerId' && value) {
      const selectedCustomer = customers.find(c => c.id === parseInt(value));
      if (selectedCustomer) {
        setFormData(prev => ({
          ...prev,
          contactPerson: selectedCustomer.contactPerson
        }));
      }
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    // Required fields
    if (!formData.customerId) newErrors.customerId = 'Customer is required';
    if (!formData.type.trim()) newErrors.type = 'Service type is required';
    if (!formData.description.trim()) newErrors.description = 'Description is required';
    if (!formData.scheduledDate) newErrors.scheduledDate = 'Scheduled date is required';
    if (!formData.assignedTo) newErrors.assignedTo = 'Technician assignment is required';
    if (!formData.amount.trim()) newErrors.amount = 'Amount is required';
    
    // Amount validation
    if (formData.amount && (isNaN(formData.amount) || parseFloat(formData.amount) <= 0)) {
      newErrors.amount = 'Please enter a valid amount';
    }
    
    // Hours validation
    if (formData.estimatedHours && (isNaN(formData.estimatedHours) || parseFloat(formData.estimatedHours) <= 0)) {
      newErrors.estimatedHours = 'Please enter valid estimated hours';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast.error('Please fix the errors in the form');
      return;
    }
    
    setLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      toast.success(isEdit ? 'Service request updated successfully' : 'Service request created successfully');
      navigate('/services');
    } catch (error) {
      toast.error('Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/services');
  };

  if (loading && isEdit) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container-fluid">
      {/* Header */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h2 className="mb-0">{isEdit ? 'Edit Service Request' : 'New Service Request'}</h2>
              <p className="text-muted">
                {isEdit ? 'Update service request details' : 'Create a new service request'}
              </p>
            </div>
            <div className="d-flex gap-2">
              <button 
                type="button" 
                className="btn btn-outline-secondary"
                onClick={handleCancel}
              >
                <FaTimes className="me-2" />
                Cancel
              </button>
              <button 
                type="submit" 
                form="serviceForm"
                className="btn btn-primary"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                    {isEdit ? 'Updating...' : 'Creating...'}
                  </>
                ) : (
                  <>
                    <FaSave className="me-2" />
                    {isEdit ? 'Update Service' : 'Create Service'}
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Form */}
      <form id="serviceForm" onSubmit={handleSubmit}>
        <div className="row">
          {/* Service Information */}
          <div className="col-lg-6 mb-4">
            <div className="card h-100">
              <div className="card-header">
                <h5 className="card-title mb-0">
                  <FaTools className="me-2" />
                  Service Information
                </h5>
              </div>
              <div className="card-body">
                <div className="row">
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Service Number</label>
                    <input
                      type="text"
                      className="form-control"
                      name="serviceNumber"
                      value={formData.serviceNumber}
                      readOnly
                    />
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Customer *</label>
                    <select
                      className={`form-select ${errors.customerId ? 'is-invalid' : ''}`}
                      name="customerId"
                      value={formData.customerId}
                      onChange={handleInputChange}
                    >
                      <option value="">Select customer</option>
                      {customers.map(customer => (
                        <option key={customer.id} value={customer.id}>
                          {customer.name}
                        </option>
                      ))}
                    </select>
                    {errors.customerId && <div className="invalid-feedback">{errors.customerId}</div>}
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Service Type *</label>
                    <select
                      className={`form-select ${errors.type ? 'is-invalid' : ''}`}
                      name="type"
                      value={formData.type}
                      onChange={handleInputChange}
                    >
                      <option value="">Select service type</option>
                      <option value="Installation">Installation</option>
                      <option value="Support">Support</option>
                      <option value="Training">Training</option>
                      <option value="Maintenance">Maintenance</option>
                      <option value="Consultation">Consultation</option>
                      <option value="Data Migration">Data Migration</option>
                    </select>
                    {errors.type && <div className="invalid-feedback">{errors.type}</div>}
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Category</label>
                    <select
                      className="form-select"
                      name="category"
                      value={formData.category}
                      onChange={handleInputChange}
                    >
                      <option value="">Select category</option>
                      <option value="Software">Software</option>
                      <option value="Hardware">Hardware</option>
                      <option value="Network">Network</option>
                      <option value="Data">Data</option>
                      <option value="Training">Training</option>
                    </select>
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Priority</label>
                    <select
                      className="form-select"
                      name="priority"
                      value={formData.priority}
                      onChange={handleInputChange}
                    >
                      <option value="low">Low</option>
                      <option value="medium">Medium</option>
                      <option value="high">High</option>
                      <option value="urgent">Urgent</option>
                    </select>
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Status</label>
                    <select
                      className="form-select"
                      name="status"
                      value={formData.status}
                      onChange={handleInputChange}
                    >
                      <option value="pending">Pending</option>
                      <option value="scheduled">Scheduled</option>
                      <option value="in-progress">In Progress</option>
                      <option value="completed">Completed</option>
                      <option value="cancelled">Cancelled</option>
                    </select>
                  </div>
                  
                  <div className="col-12 mb-3">
                    <label className="form-label">Description *</label>
                    <textarea
                      className={`form-control ${errors.description ? 'is-invalid' : ''}`}
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      rows="3"
                      placeholder="Describe the service requirements"
                    ></textarea>
                    {errors.description && <div className="invalid-feedback">{errors.description}</div>}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Assignment & Scheduling */}
          <div className="col-lg-6 mb-4">
            <div className="card h-100">
              <div className="card-header">
                <h5 className="card-title mb-0">
                  <FaUser className="me-2" />
                  Assignment & Scheduling
                </h5>
              </div>
              <div className="card-body">
                <div className="row">
                  <div className="col-12 mb-3">
                    <label className="form-label">Assigned Technician *</label>
                    <select
                      className={`form-select ${errors.assignedTo ? 'is-invalid' : ''}`}
                      name="assignedTo"
                      value={formData.assignedTo}
                      onChange={handleInputChange}
                    >
                      <option value="">Select technician</option>
                      {technicians.map(tech => (
                        <option key={tech.id} value={tech.id}>
                          {tech.name} ({tech.specialization})
                        </option>
                      ))}
                    </select>
                    {errors.assignedTo && <div className="invalid-feedback">{errors.assignedTo}</div>}
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Scheduled Date *</label>
                    <input
                      type="date"
                      className={`form-control ${errors.scheduledDate ? 'is-invalid' : ''}`}
                      name="scheduledDate"
                      value={formData.scheduledDate}
                      onChange={handleInputChange}
                      min={new Date().toISOString().split('T')[0]}
                    />
                    {errors.scheduledDate && <div className="invalid-feedback">{errors.scheduledDate}</div>}
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Scheduled Time</label>
                    <input
                      type="time"
                      className="form-control"
                      name="scheduledTime"
                      value={formData.scheduledTime}
                      onChange={handleInputChange}
                    />
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Estimated Hours</label>
                    <input
                      type="number"
                      className={`form-control ${errors.estimatedHours ? 'is-invalid' : ''}`}
                      name="estimatedHours"
                      value={formData.estimatedHours}
                      onChange={handleInputChange}
                      placeholder="0"
                      min="0"
                      step="0.5"
                    />
                    {errors.estimatedHours && <div className="invalid-feedback">{errors.estimatedHours}</div>}
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Service Location</label>
                    <select
                      className="form-select"
                      name="serviceLocation"
                      value={formData.serviceLocation}
                      onChange={handleInputChange}
                    >
                      <option value="customer-site">Customer Site</option>
                      <option value="office">Our Office</option>
                      <option value="remote">Remote</option>
                    </select>
                  </div>
                  
                  {formData.serviceLocation === 'customer-site' && (
                    <>
                      <div className="col-12 mb-3">
                        <label className="form-label">Service Address</label>
                        <textarea
                          className="form-control"
                          name="address"
                          value={formData.address}
                          onChange={handleInputChange}
                          rows="2"
                          placeholder="Enter service address"
                        ></textarea>
                      </div>
                      
                      <div className="col-md-6 mb-3">
                        <label className="form-label">City</label>
                        <input
                          type="text"
                          className="form-control"
                          name="city"
                          value={formData.city}
                          onChange={handleInputChange}
                          placeholder="Enter city"
                        />
                      </div>
                      
                      <div className="col-md-6 mb-3">
                        <label className="form-label">State</label>
                        <select
                          className="form-select"
                          name="state"
                          value={formData.state}
                          onChange={handleInputChange}
                        >
                          <option value="">Select state</option>
                          <option value="Maharashtra">Maharashtra</option>
                          <option value="Delhi">Delhi</option>
                          <option value="Karnataka">Karnataka</option>
                          <option value="Tamil Nadu">Tamil Nadu</option>
                          <option value="Gujarat">Gujarat</option>
                        </select>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="row">
          {/* Financial Information */}
          <div className="col-lg-6 mb-4">
            <div className="card h-100">
              <div className="card-header">
                <h5 className="card-title mb-0">
                  <FaRupeeSign className="me-2" />
                  Financial Information
                </h5>
              </div>
              <div className="card-body">
                <div className="row">
                  <div className="col-md-8 mb-3">
                    <label className="form-label">Service Amount *</label>
                    <input
                      type="number"
                      className={`form-control ${errors.amount ? 'is-invalid' : ''}`}
                      name="amount"
                      value={formData.amount}
                      onChange={handleInputChange}
                      placeholder="0"
                      min="0"
                      step="0.01"
                    />
                    {errors.amount && <div className="invalid-feedback">{errors.amount}</div>}
                  </div>
                  
                  <div className="col-md-4 mb-3">
                    <label className="form-label">Currency</label>
                    <select
                      className="form-select"
                      name="currency"
                      value={formData.currency}
                      onChange={handleInputChange}
                    >
                      <option value="INR">INR</option>
                      <option value="USD">USD</option>
                      <option value="EUR">EUR</option>
                    </select>
                  </div>
                  
                  <div className="col-12 mb-3">
                    <label className="form-label">Payment Status</label>
                    <select
                      className="form-select"
                      name="paymentStatus"
                      value={formData.paymentStatus}
                      onChange={handleInputChange}
                    >
                      <option value="pending">Pending</option>
                      <option value="partial">Partial</option>
                      <option value="paid">Paid</option>
                      <option value="overdue">Overdue</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Additional Details */}
          <div className="col-lg-6 mb-4">
            <div className="card h-100">
              <div className="card-header">
                <h5 className="card-title mb-0">Additional Details</h5>
              </div>
              <div className="card-body">
                <div className="row">
                  <div className="col-12 mb-3">
                    <label className="form-label">Requirements</label>
                    <textarea
                      className="form-control"
                      name="requirements"
                      value={formData.requirements}
                      onChange={handleInputChange}
                      rows="3"
                      placeholder="Special requirements or prerequisites"
                    ></textarea>
                  </div>
                  
                  <div className="col-12 mb-3">
                    <label className="form-label">Notes</label>
                    <textarea
                      className="form-control"
                      name="notes"
                      value={formData.notes}
                      onChange={handleInputChange}
                      rows="3"
                      placeholder="Additional notes or comments"
                    ></textarea>
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Contact Person</label>
                    <input
                      type="text"
                      className="form-control"
                      name="contactPerson"
                      value={formData.contactPerson}
                      onChange={handleInputChange}
                      placeholder="Contact person name"
                    />
                  </div>
                  
                  <div className="col-md-6 mb-3">
                    <label className="form-label">Contact Phone</label>
                    <input
                      type="tel"
                      className="form-control"
                      name="contactPhone"
                      value={formData.contactPhone}
                      onChange={handleInputChange}
                      placeholder="+91 **********"
                    />
                  </div>
                  
                  <div className="col-12 mb-3">
                    <label className="form-label">Contact Email</label>
                    <input
                      type="email"
                      className="form-control"
                      name="contactEmail"
                      value={formData.contactEmail}
                      onChange={handleInputChange}
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
};

export default ServiceForm;
