{"name": "tallycrm-frontend", "version": "1.0.0", "description": "Frontend React App for TallyCRM SaaS - CRM for Tally Resellers", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext js,jsx --fix", "format": "prettier --write src/", "validate": "npm run lint && npm run test", "analyze": "npm run build && npx vite-bundle-analyzer dist/stats.html"}, "keywords": ["react", "vite", "crm", "tally", "saas", "bootstrap", "frontend"], "author": "Cloudstier Solutions", "license": "PROPRIETARY", "dependencies": {"axios": "^1.6.2", "bootstrap": "^5.3.2", "bootstrap-icons": "^1.11.2", "classnames": "^2.3.2", "file-saver": "^2.0.5", "lodash": "^4.17.21", "moment": "^2.29.4", "react": "^18.2.0", "react-bootstrap": "^2.9.1", "react-datepicker": "^4.24.0", "react-dom": "^18.2.0", "react-error-boundary": "^4.0.11", "react-excel-renderer": "^1.1.0", "react-helmet-async": "^2.0.4", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-icons": "5.5.0", "react-query": "^3.39.3", "react-router-dom": "^6.20.1", "react-select": "^5.8.0", "react-table": "^7.8.0", "recharts": "^2.8.0", "xlsx": "^0.18.5", "zustand": "^4.4.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "@vitest/ui": "^1.0.4", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "husky": "^8.0.3", "jsdom": "^23.0.1", "lint-staged": "^15.2.0", "prettier": "^3.1.1", "sass": "^1.69.5", "vite": "^5.0.8", "vite-bundle-analyzer": "^0.7.0", "vitest": "^1.0.4"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/cloudstier/tallycrm.git"}, "bugs": {"url": "https://github.com/cloudstier/tallycrm/issues"}, "homepage": "https://github.com/cloudstier/tallycrm#readme", "lint-staged": {"*.{js,jsx}": ["eslint --fix", "prettier --write"]}}