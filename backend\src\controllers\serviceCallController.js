import models from '../models/index.js';
import { logger } from '../utils/logger.js';
import { Op } from 'sequelize';

/**
 * Get all service calls with pagination and filters
 */
export const getServiceCalls = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      search,
      customerId,
      statusId,
      assignedTo,
      callType,
      priority,
      isUnderAmc,
      dateFrom,
      dateTo,
      sortBy = 'created_at',
      sortOrder = 'DESC',
    } = req.query;

    const offset = (page - 1) * limit;
    const where = { tenant_id: req.user.tenant.id };

    // Apply filters
    if (search) {
      where[Op.or] = [
        { call_number: { [Op.iLike]: `%${search}%` } },
        { subject: { [Op.iLike]: `%${search}%` } },
        { description: { [Op.iLike]: `%${search}%` } },
      ];
    }

    if (customerId) {
      where.customer_id = customerId;
    }

    if (statusId) {
      where.status_id = statusId;
    }

    if (assignedTo) {
      where.assigned_to = assignedTo;
    }

    if (callType) {
      where.call_type = callType;
    }

    if (priority) {
      where.priority = priority;
    }

    if (isUnderAmc !== undefined) {
      where.is_under_amc = isUnderAmc === 'true';
    }

    if (dateFrom || dateTo) {
      where.call_date = {};
      if (dateFrom) {
        where.call_date[Op.gte] = new Date(dateFrom);
      }
      if (dateTo) {
        where.call_date[Op.lte] = new Date(dateTo);
      }
    }

    const { count, rows: serviceCalls } = await models.ServiceCall.findAndCountAll({
      where,
      include: [
        {
          model: models.Customer,
          as: 'customer',
          attributes: ['id', 'customer_code', 'company_name', 'phone', 'email'],
        },
        {
          model: models.CustomerContact,
          as: 'contactPerson',
          attributes: ['id', 'first_name', 'last_name', 'phone', 'email'],
        },
        {
          model: models.CallStatus,
          as: 'status',
          attributes: ['id', 'name', 'code', 'color', 'category'],
        },
        {
          model: models.Executive,
          as: 'assignedExecutive',
          attributes: ['id', 'first_name', 'last_name', 'phone', 'email'],
        },
        {
          model: models.NatureOfIssue,
          as: 'natureOfIssue',
          attributes: ['id', 'name', 'category', 'severity'],
        },
        {
          model: models.Area,
          as: 'area',
          attributes: ['id', 'name', 'city', 'state'],
        },
        {
          model: models.User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email'],
        },
      ],
      limit: parseInt(limit),
      offset,
      order: [[sortBy, sortOrder.toUpperCase()]],
    });

    res.json({
      success: true,
      data: {
        serviceCalls,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: parseInt(limit),
        },
      },
    });

  } catch (error) {
    logger.error('Get service calls error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch service calls',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get service call by ID
 */
export const getServiceCallById = async (req, res) => {
  try {
    const { id } = req.params;

    const serviceCall = await models.ServiceCall.findOne({
      where: {
        id,
        tenant_id: req.user.tenant.id,
      },
      include: [
        {
          model: models.Customer,
          as: 'customer',
          include: [
            {
              model: models.Industry,
              as: 'industry',
            },
            {
              model: models.Area,
              as: 'area',
            },
          ],
        },
        {
          model: models.CustomerContact,
          as: 'contactPerson',
          include: [
            {
              model: models.Designation,
              as: 'designation',
            },
          ],
        },
        {
          model: models.CustomerTSS,
          as: 'tss',
          include: [
            {
              model: models.LicenseEdition,
              as: 'licenseEdition',
            },
          ],
        },
        {
          model: models.CustomerAMC,
          as: 'amc',
        },
        {
          model: models.CallStatus,
          as: 'status',
        },
        {
          model: models.Executive,
          as: 'assignedExecutive',
          include: [
            {
              model: models.Designation,
              as: 'designation',
            },
          ],
        },
        {
          model: models.NatureOfIssue,
          as: 'natureOfIssue',
        },
        {
          model: models.Area,
          as: 'area',
        },
        {
          model: models.ServiceCallItem,
          as: 'items',
          include: [
            {
              model: models.TallyProduct,
              as: 'product',
            },
            {
              model: models.AdditionalService,
              as: 'additionalService',
            },
          ],
        },
        {
          model: models.User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email'],
        },
      ],
    });

    if (!serviceCall) {
      return res.status(404).json({
        success: false,
        message: 'Service call not found',
      });
    }

    res.json({
      success: true,
      data: { serviceCall },
    });

  } catch (error) {
    logger.error('Get service call by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch service call',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Create new service call
 */
export const createServiceCall = async (req, res) => {
  try {
    const serviceCallData = {
      ...req.body,
      tenant_id: req.user.tenant.id,
      created_by: req.user.id,
    };

    // Generate call number if not provided
    if (!serviceCallData.call_number) {
      const lastCall = await models.ServiceCall.findOne({
        where: { tenant_id: req.user.tenant.id },
        order: [['created_at', 'DESC']],
      });

      const nextNumber = lastCall ? 
        parseInt(lastCall.call_number.replace(/\D/g, '')) + 1 : 1;
      serviceCallData.call_number = `SC${nextNumber.toString().padStart(6, '0')}`;
    }

    // Get default status (Open)
    if (!serviceCallData.status_id) {
      const defaultStatus = await models.CallStatus.findOne({
        where: { code: 'OPEN' },
      });
      if (defaultStatus) {
        serviceCallData.status_id = defaultStatus.id;
      }
    }

    // Check if customer exists and belongs to tenant
    const customer = await models.Customer.findOne({
      where: {
        id: serviceCallData.customer_id,
        tenant_id: req.user.tenant.id,
      },
    });

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: 'Customer not found',
      });
    }

    // Check AMC if specified
    if (serviceCallData.amc_id) {
      const amc = await models.CustomerAMC.findOne({
        where: {
          id: serviceCallData.amc_id,
          customer_id: serviceCallData.customer_id,
        },
      });

      if (!amc) {
        return res.status(404).json({
          success: false,
          message: 'AMC contract not found for this customer',
        });
      }

      if (!amc.canCreateServiceCall()) {
        return res.status(400).json({
          success: false,
          message: 'Cannot create service call under this AMC contract',
          details: {
            status: amc.status,
            callsUsed: amc.calls_used,
            callsAllowed: amc.calls_allowed,
          },
        });
      }

      serviceCallData.is_under_amc = true;
    }

    const transaction = await models.sequelize.transaction();

    try {
      // Create service call
      const serviceCall = await models.ServiceCall.create(serviceCallData, { transaction });

      // Update AMC usage if applicable
      if (serviceCallData.amc_id) {
        await models.CustomerAMC.increment('calls_used', {
          by: 1,
          where: { id: serviceCallData.amc_id },
          transaction,
        });
      }

      // Create service call items if provided
      if (req.body.items && req.body.items.length > 0) {
        const items = req.body.items.map(item => ({
          ...item,
          service_call_id: serviceCall.id,
        }));

        for (const item of items) {
          const serviceCallItem = await models.ServiceCallItem.create(item, { transaction });
          serviceCallItem.updateCalculatedFields();
          await serviceCallItem.save({ transaction });
        }
      }

      await transaction.commit();

      // Fetch the created service call with associations
      const createdServiceCall = await models.ServiceCall.findByPk(serviceCall.id, {
        include: [
          {
            model: models.Customer,
            as: 'customer',
          },
          {
            model: models.CallStatus,
            as: 'status',
          },
          {
            model: models.Executive,
            as: 'assignedExecutive',
          },
          {
            model: models.ServiceCallItem,
            as: 'items',
          },
        ],
      });

      logger.info('Service call created successfully:', {
        serviceCallId: serviceCall.id,
        callNumber: serviceCall.call_number,
        customerId: serviceCall.customer_id,
        createdBy: req.user.id,
      });

      res.status(201).json({
        success: true,
        message: 'Service call created successfully',
        data: { serviceCall: createdServiceCall },
      });

    } catch (error) {
      await transaction.rollback();
      throw error;
    }

  } catch (error) {
    logger.error('Create service call error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create service call',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Update service call
 */
export const updateServiceCall = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const serviceCall = await models.ServiceCall.findOne({
      where: {
        id,
        tenant_id: req.user.tenant.id,
      },
      include: [
        {
          model: models.CallStatus,
          as: 'status',
        },
      ],
    });

    if (!serviceCall) {
      return res.status(404).json({
        success: false,
        message: 'Service call not found',
      });
    }

    // Check if status change is valid
    if (updateData.status_id && updateData.status_id !== serviceCall.status_id) {
      const newStatus = await models.CallStatus.findByPk(updateData.status_id);
      
      if (!newStatus) {
        return res.status(404).json({
          success: false,
          message: 'Invalid status',
        });
      }

      if (!serviceCall.status.canTransitionTo(newStatus)) {
        return res.status(400).json({
          success: false,
          message: `Cannot transition from ${serviceCall.status.name} to ${newStatus.name}`,
        });
      }

      // Set completion/closure timestamps
      if (newStatus.category === 'resolved' && !serviceCall.completed_at) {
        updateData.completed_at = new Date();
      }
      
      if (newStatus.category === 'closed' && !serviceCall.closed_at) {
        updateData.closed_at = new Date();
      }
    }

    await serviceCall.update(updateData);

    // Fetch updated service call with associations
    const updatedServiceCall = await models.ServiceCall.findByPk(serviceCall.id, {
      include: [
        {
          model: models.Customer,
          as: 'customer',
        },
        {
          model: models.CallStatus,
          as: 'status',
        },
        {
          model: models.Executive,
          as: 'assignedExecutive',
        },
      ],
    });

    logger.info('Service call updated successfully:', {
      serviceCallId: serviceCall.id,
      callNumber: serviceCall.call_number,
      updatedBy: req.user.id,
    });

    res.json({
      success: true,
      message: 'Service call updated successfully',
      data: { serviceCall: updatedServiceCall },
    });

  } catch (error) {
    logger.error('Update service call error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update service call',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get service call statistics
 */
export const getServiceCallStats = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;

    const stats = await Promise.all([
      // Total service calls
      models.ServiceCall.count({
        where: { tenant_id: tenantId },
      }),
      
      // Calls by status
      models.ServiceCall.findAll({
        where: { tenant_id: tenantId },
        include: [
          {
            model: models.CallStatus,
            as: 'status',
            attributes: ['name', 'category', 'color'],
          },
        ],
        attributes: [
          [models.sequelize.fn('COUNT', models.sequelize.col('ServiceCall.id')), 'count'],
        ],
        group: ['status.id', 'status.name', 'status.category', 'status.color'],
        raw: true,
      }),
      
      // Calls by priority
      models.ServiceCall.findAll({
        where: { tenant_id: tenantId },
        attributes: [
          'priority',
          [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
        ],
        group: ['priority'],
        raw: true,
      }),
      
      // Recent calls (last 30 days)
      models.ServiceCall.count({
        where: {
          tenant_id: tenantId,
          call_date: {
            [Op.gte]: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          },
        },
      }),
    ]);

    const [totalCalls, callsByStatus, callsByPriority, recentCalls] = stats;

    res.json({
      success: true,
      data: {
        totalCalls,
        recentCalls,
        callsByStatus: callsByStatus.map(item => ({
          status: item['status.name'],
          category: item['status.category'],
          color: item['status.color'],
          count: parseInt(item.count),
        })),
        callsByPriority: callsByPriority.reduce((acc, item) => {
          acc[item.priority] = parseInt(item.count);
          return acc;
        }, {}),
      },
    });

  } catch (error) {
    logger.error('Get service call stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch service call statistics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};
