import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const CustomerContact = sequelize.define('CustomerContact', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    customer_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'customers',
        key: 'id',
      },
    },
    contact_type: {
      type: DataTypes.ENUM('primary', 'secondary', 'technical', 'accounts', 'decision_maker'),
      allowNull: false,
      defaultValue: 'primary',
    },
    title: {
      type: DataTypes.ENUM('Mr', 'Ms', 'Mrs', 'Dr', 'Prof'),
      allowNull: true,
    },
    first_name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [2, 50],
      },
    },
    last_name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [2, 50],
      },
    },
    designation_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'designations',
        key: 'id',
      },
    },
    department: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        isEmail: true,
      },
    },
    phone: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [10, 15],
      },
    },
    mobile: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [10, 15],
      },
    },
    whatsapp: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        len: [10, 15],
      },
    },
    extension: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    date_of_birth: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    anniversary_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    address_line_1: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    address_line_2: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    city: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    state: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    country: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: 'India',
    },
    postal_code: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    is_decision_maker: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    is_technical_contact: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    is_billing_contact: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    preferred_communication: {
      type: DataTypes.ENUM('email', 'phone', 'whatsapp', 'sms'),
      allowNull: true,
      defaultValue: 'email',
    },
    communication_preferences: {
      type: DataTypes.JSONB,
      defaultValue: {},
      comment: 'Communication preferences and settings',
    },
    social_media: {
      type: DataTypes.JSONB,
      defaultValue: {},
      comment: 'Social media profiles',
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'customer_contacts',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['customer_id'],
      },
      {
        fields: ['contact_type'],
      },
      {
        fields: ['email'],
      },
      {
        fields: ['phone'],
      },
      {
        fields: ['mobile'],
      },
      {
        fields: ['is_decision_maker'],
      },
      {
        fields: ['is_technical_contact'],
      },
      {
        fields: ['is_billing_contact'],
      },
      {
        fields: ['is_active'],
      },
    ],
  });

  // Instance methods
  CustomerContact.prototype.getFullName = function() {
    const title = this.title ? `${this.title}. ` : '';
    return `${title}${this.first_name} ${this.last_name}`;
  };

  CustomerContact.prototype.getDisplayName = function() {
    return `${this.first_name} ${this.last_name}`;
  };

  CustomerContact.prototype.getPrimaryPhone = function() {
    return this.mobile || this.phone;
  };

  CustomerContact.prototype.getFullAddress = function() {
    const parts = [
      this.address_line_1,
      this.address_line_2,
      this.city,
      this.state,
      this.country,
      this.postal_code,
    ].filter(Boolean);
    return parts.join(', ');
  };

  CustomerContact.prototype.getAge = function() {
    if (!this.date_of_birth) return null;
    const today = new Date();
    const birthDate = new Date(this.date_of_birth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  CustomerContact.prototype.getUpcomingBirthday = function() {
    if (!this.date_of_birth) return null;
    const today = new Date();
    const currentYear = today.getFullYear();
    const birthDate = new Date(this.date_of_birth);
    const thisYearBirthday = new Date(currentYear, birthDate.getMonth(), birthDate.getDate());
    
    if (thisYearBirthday < today) {
      thisYearBirthday.setFullYear(currentYear + 1);
    }
    
    return thisYearBirthday;
  };

  CustomerContact.prototype.getUpcomingAnniversary = function() {
    if (!this.anniversary_date) return null;
    const today = new Date();
    const currentYear = today.getFullYear();
    const anniversaryDate = new Date(this.anniversary_date);
    const thisYearAnniversary = new Date(currentYear, anniversaryDate.getMonth(), anniversaryDate.getDate());
    
    if (thisYearAnniversary < today) {
      thisYearAnniversary.setFullYear(currentYear + 1);
    }
    
    return thisYearAnniversary;
  };

  // Associations
  CustomerContact.associate = function(models) {
    CustomerContact.belongsTo(models.Customer, {
      foreignKey: 'customer_id',
      as: 'customer',
    });

    CustomerContact.belongsTo(models.Designation, {
      foreignKey: 'designation_id',
      as: 'designation',
    });

    CustomerContact.hasMany(models.ServiceCall, {
      foreignKey: 'contact_person_id',
      as: 'serviceCalls',
    });
  };

  return CustomerContact;
}
