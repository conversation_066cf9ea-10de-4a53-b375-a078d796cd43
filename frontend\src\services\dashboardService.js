import { apiService } from './api';

// Dashboard service for real data fetching
export const dashboardService = {
  // Get dashboard statistics
  getStats: async () => {
    try {
      const response = await apiService.get('/dashboard/stats');
      return response.data;
    } catch (error) {
      console.error('Failed to fetch dashboard stats:', error);
      // Return fallback data for development
      return {
        totalCustomers: 0,
        activeServices: 0,
        monthlyRevenue: 0,
        pendingTasks: 0,
        totalSales: 0,
        activeLicenses: 0,
        expiringAMCs: 0,
        openServiceCalls: 0
      };
    }
  },

  // Get recent activities
  getRecentActivities: async () => {
    try {
      const response = await apiService.get('/dashboard/activities');
      return response.data;
    } catch (error) {
      console.error('Failed to fetch recent activities:', error);
      // Return fallback data
      return [];
    }
  },

  // Get chart data
  getChartData: async (type) => {
    try {
      const response = await apiService.get(`/dashboard/charts/${type}`);
      return response.data;
    } catch (error) {
      console.error(`Failed to fetch chart data for ${type}:`, error);
      // Return fallback data based on chart type
      switch (type) {
        case 'sales':
          return {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [{
              label: 'Sales',
              data: [0, 0, 0, 0, 0, 0],
              borderColor: 'rgb(75, 192, 192)',
              backgroundColor: 'rgba(75, 192, 192, 0.2)',
            }]
          };
        case 'customers':
          return {
            labels: ['New', 'Active', 'Inactive'],
            datasets: [{
              data: [0, 0, 0],
              backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56']
            }]
          };
        default:
          return { labels: [], datasets: [] };
      }
    }
  },

  // Get customers data
  getCustomers: async (params = {}) => {
    try {
      const response = await apiService.get('/customers', { params });
      return response.data;
    } catch (error) {
      console.error('Failed to fetch customers:', error);
      return {
        customers: [],
        pagination: {
          page: 1,
          limit: 10,
          total: 0,
          pages: 0
        }
      };
    }
  },

  // Get service calls data
  getServiceCalls: async (params = {}) => {
    try {
      const response = await apiService.get('/service-calls', { params });
      return response.data;
    } catch (error) {
      console.error('Failed to fetch service calls:', error);
      return {
        serviceCalls: [],
        pagination: {
          page: 1,
          limit: 10,
          total: 0,
          pages: 0
        }
      };
    }
  },

  // Get sales data
  getSales: async (params = {}) => {
    try {
      const response = await apiService.get('/sales', { params });
      return response.data;
    } catch (error) {
      console.error('Failed to fetch sales:', error);
      return {
        sales: [],
        pagination: {
          page: 1,
          limit: 10,
          total: 0,
          pages: 0
        }
      };
    }
  }
};

export default dashboardService;
