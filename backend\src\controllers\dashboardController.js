import models from '../models/index.js';
import { logger } from '../utils/logger.js';
import { Op } from 'sequelize';

/**
 * Get dashboard overview statistics
 */
export const getDashboardOverview = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const today = new Date();
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const startOfYear = new Date(today.getFullYear(), 0, 1);
    const last30Days = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

    // Get basic counts
    const [
      totalCustomers,
      activeCustomers,
      totalServiceCalls,
      openServiceCalls,
      totalSales,
      totalExecutives,
      activeExecutives,
    ] = await Promise.all([
      models.Customer.count({
        where: { tenant_id: tenantId },
      }),
      models.Customer.count({
        where: { tenant_id: tenantId, is_active: true },
      }),
      models.ServiceCall.count({
        where: { tenant_id: tenantId },
      }),
      models.ServiceCall.count({
        where: {
          tenant_id: tenantId,
          '$status.category$': 'open',
        },
        include: [
          {
            model: models.CallStatus,
            as: 'status',
            attributes: [],
          },
        ],
      }),
      models.Sale.count({
        where: { tenant_id: tenantId },
      }),
      models.Executive.count({
        where: { tenant_id: tenantId },
      }),
      models.Executive.count({
        where: { tenant_id: tenantId, is_active: true },
      }),
    ]);

    // Get recent activity counts
    const [
      newCustomersThisMonth,
      serviceCallsThisMonth,
      salesThisMonth,
      serviceCallsLast30Days,
    ] = await Promise.all([
      models.Customer.count({
        where: {
          tenant_id: tenantId,
          created_at: { [Op.gte]: startOfMonth },
        },
      }),
      models.ServiceCall.count({
        where: {
          tenant_id: tenantId,
          call_date: { [Op.gte]: startOfMonth },
        },
      }),
      models.Sale.count({
        where: {
          tenant_id: tenantId,
          sale_date: { [Op.gte]: startOfMonth },
        },
      }),
      models.ServiceCall.count({
        where: {
          tenant_id: tenantId,
          call_date: { [Op.gte]: last30Days },
        },
      }),
    ]);

    // Get revenue statistics
    const [salesRevenue, amcRevenue] = await Promise.all([
      models.Sale.findAll({
        where: {
          tenant_id: tenantId,
          sale_date: { [Op.gte]: startOfYear },
        },
        attributes: [
          [models.sequelize.fn('SUM', models.sequelize.col('total_amount')), 'total'],
          [models.sequelize.fn('SUM', models.sequelize.col('paid_amount')), 'paid'],
        ],
        raw: true,
      }),
      models.CustomerAMC.findAll({
        where: {
          '$customer.tenant_id$': tenantId,
          start_date: { [Op.gte]: startOfYear },
        },
        include: [
          {
            model: models.Customer,
            as: 'customer',
            attributes: [],
          },
        ],
        attributes: [
          [models.sequelize.fn('SUM', models.sequelize.col('contract_value')), 'total'],
        ],
        raw: true,
      }),
    ]);

    // Get service call priority distribution
    const serviceCallsByPriority = await models.ServiceCall.findAll({
      where: { tenant_id: tenantId },
      attributes: [
        'priority',
        [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
      ],
      group: ['priority'],
      raw: true,
    });

    // Get customer type distribution
    const customersByType = await models.Customer.findAll({
      where: { tenant_id: tenantId },
      attributes: [
        'customer_type',
        [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
      ],
      group: ['customer_type'],
      raw: true,
    });

    // Get recent service calls
    const recentServiceCalls = await models.ServiceCall.findAll({
      where: { tenant_id: tenantId },
      include: [
        {
          model: models.Customer,
          as: 'customer',
          attributes: ['id', 'company_name', 'customer_code'],
        },
        {
          model: models.CallStatus,
          as: 'status',
          attributes: ['id', 'name', 'color', 'category'],
        },
        {
          model: models.Executive,
          as: 'assignedExecutive',
          attributes: ['id', 'first_name', 'last_name'],
        },
      ],
      order: [['created_at', 'DESC']],
      limit: 10,
    });

    // Get upcoming follow-ups
    const upcomingFollowUps = await models.ServiceCall.findAll({
      where: {
        tenant_id: tenantId,
        follow_up_required: true,
        follow_up_date: {
          [Op.gte]: today,
          [Op.lte]: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Next 7 days
        },
      },
      include: [
        {
          model: models.Customer,
          as: 'customer',
          attributes: ['id', 'company_name', 'customer_code'],
        },
        {
          model: models.Executive,
          as: 'assignedExecutive',
          attributes: ['id', 'first_name', 'last_name'],
        },
      ],
      order: [['follow_up_date', 'ASC']],
      limit: 10,
    });

    // Get expiring AMCs
    const expiringAMCs = await models.CustomerAMC.findAll({
      where: {
        '$customer.tenant_id$': tenantId,
        status: 'active',
        end_date: {
          [Op.gte]: today,
          [Op.lte]: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // Next 30 days
        },
      },
      include: [
        {
          model: models.Customer,
          as: 'customer',
          attributes: ['id', 'company_name', 'customer_code'],
        },
      ],
      order: [['end_date', 'ASC']],
      limit: 10,
    });

    // Calculate growth percentages (compared to previous month)
    const previousMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
    const endOfPreviousMonth = new Date(today.getFullYear(), today.getMonth(), 0);

    const [
      customersLastMonth,
      serviceCallsLastMonth,
      salesLastMonth,
    ] = await Promise.all([
      models.Customer.count({
        where: {
          tenant_id: tenantId,
          created_at: {
            [Op.gte]: previousMonth,
            [Op.lte]: endOfPreviousMonth,
          },
        },
      }),
      models.ServiceCall.count({
        where: {
          tenant_id: tenantId,
          call_date: {
            [Op.gte]: previousMonth,
            [Op.lte]: endOfPreviousMonth,
          },
        },
      }),
      models.Sale.count({
        where: {
          tenant_id: tenantId,
          sale_date: {
            [Op.gte]: previousMonth,
            [Op.lte]: endOfPreviousMonth,
          },
        },
      }),
    ]);

    // Calculate growth percentages
    const calculateGrowth = (current, previous) => {
      if (previous === 0) return current > 0 ? 100 : 0;
      return Math.round(((current - previous) / previous) * 100);
    };

    const overview = {
      summary: {
        totalCustomers,
        activeCustomers,
        totalServiceCalls,
        openServiceCalls,
        totalSales,
        totalExecutives,
        activeExecutives,
      },
      thisMonth: {
        newCustomers: newCustomersThisMonth,
        serviceCalls: serviceCallsThisMonth,
        sales: salesThisMonth,
      },
      growth: {
        customers: calculateGrowth(newCustomersThisMonth, customersLastMonth),
        serviceCalls: calculateGrowth(serviceCallsThisMonth, serviceCallsLastMonth),
        sales: calculateGrowth(salesThisMonth, salesLastMonth),
      },
      revenue: {
        salesTotal: parseFloat(salesRevenue[0]?.total || 0),
        salesPaid: parseFloat(salesRevenue[0]?.paid || 0),
        amcTotal: parseFloat(amcRevenue[0]?.total || 0),
      },
      distributions: {
        serviceCallsByPriority: serviceCallsByPriority.reduce((acc, item) => {
          acc[item.priority] = parseInt(item.count);
          return acc;
        }, {}),
        customersByType: customersByType.reduce((acc, item) => {
          acc[item.customer_type] = parseInt(item.count);
          return acc;
        }, {}),
      },
      recentActivity: {
        serviceCallsLast30Days,
        recentServiceCalls: recentServiceCalls.map(call => ({
          id: call.id,
          callNumber: call.call_number,
          subject: call.subject,
          priority: call.priority,
          customer: call.customer,
          status: call.status,
          assignedExecutive: call.assignedExecutive,
          createdAt: call.created_at,
        })),
      },
      alerts: {
        upcomingFollowUps: upcomingFollowUps.map(call => ({
          id: call.id,
          callNumber: call.call_number,
          subject: call.subject,
          followUpDate: call.follow_up_date,
          customer: call.customer,
          assignedExecutive: call.assignedExecutive,
        })),
        expiringAMCs: expiringAMCs.map(amc => ({
          id: amc.id,
          amcNumber: amc.amc_number,
          endDate: amc.end_date,
          contractValue: amc.contract_value,
          customer: amc.customer,
          daysRemaining: Math.ceil((new Date(amc.end_date) - today) / (1000 * 60 * 60 * 24)),
        })),
      },
    };

    res.json({
      success: true,
      data: overview,
    });

  } catch (error) {
    logger.error('Get dashboard overview error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch dashboard overview',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};

/**
 * Get dashboard charts data
 */
export const getDashboardCharts = async (req, res) => {
  try {
    const tenantId = req.user.tenant.id;
    const { period = '30d' } = req.query;

    let startDate;
    let groupBy;
    let dateFormat;

    switch (period) {
      case '7d':
        startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        groupBy = 'day';
        dateFormat = 'YYYY-MM-DD';
        break;
      case '30d':
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        groupBy = 'day';
        dateFormat = 'YYYY-MM-DD';
        break;
      case '3m':
        startDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
        groupBy = 'week';
        dateFormat = 'YYYY-WW';
        break;
      case '1y':
        startDate = new Date(Date.now() - 365 * 24 * 60 * 60 * 1000);
        groupBy = 'month';
        dateFormat = 'YYYY-MM';
        break;
      default:
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        groupBy = 'day';
        dateFormat = 'YYYY-MM-DD';
    }

    // Service calls trend
    const serviceCallsTrend = await models.ServiceCall.findAll({
      where: {
        tenant_id: tenantId,
        call_date: { [Op.gte]: startDate },
      },
      attributes: [
        [models.sequelize.fn('DATE_TRUNC', groupBy, models.sequelize.col('call_date')), 'date'],
        [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
      ],
      group: [models.sequelize.fn('DATE_TRUNC', groupBy, models.sequelize.col('call_date'))],
      order: [[models.sequelize.fn('DATE_TRUNC', groupBy, models.sequelize.col('call_date')), 'ASC']],
      raw: true,
    });

    // Sales trend
    const salesTrend = await models.Sale.findAll({
      where: {
        tenant_id: tenantId,
        sale_date: { [Op.gte]: startDate },
      },
      attributes: [
        [models.sequelize.fn('DATE_TRUNC', groupBy, models.sequelize.col('sale_date')), 'date'],
        [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
        [models.sequelize.fn('SUM', models.sequelize.col('total_amount')), 'amount'],
      ],
      group: [models.sequelize.fn('DATE_TRUNC', groupBy, models.sequelize.col('sale_date'))],
      order: [[models.sequelize.fn('DATE_TRUNC', groupBy, models.sequelize.col('sale_date')), 'ASC']],
      raw: true,
    });

    // Customer acquisition trend
    const customersTrend = await models.Customer.findAll({
      where: {
        tenant_id: tenantId,
        created_at: { [Op.gte]: startDate },
      },
      attributes: [
        [models.sequelize.fn('DATE_TRUNC', groupBy, models.sequelize.col('created_at')), 'date'],
        [models.sequelize.fn('COUNT', models.sequelize.col('id')), 'count'],
      ],
      group: [models.sequelize.fn('DATE_TRUNC', groupBy, models.sequelize.col('created_at'))],
      order: [[models.sequelize.fn('DATE_TRUNC', groupBy, models.sequelize.col('created_at')), 'ASC']],
      raw: true,
    });

    res.json({
      success: true,
      data: {
        period,
        serviceCallsTrend: serviceCallsTrend.map(item => ({
          date: item.date,
          count: parseInt(item.count),
        })),
        salesTrend: salesTrend.map(item => ({
          date: item.date,
          count: parseInt(item.count),
          amount: parseFloat(item.amount || 0),
        })),
        customersTrend: customersTrend.map(item => ({
          date: item.date,
          count: parseInt(item.count),
        })),
      },
    });

  } catch (error) {
    logger.error('Get dashboard charts error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch dashboard charts data',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }
};
