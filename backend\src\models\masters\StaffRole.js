import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const StaffRole = sequelize.define('StaffRole', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [2, 100],
      },
    },
    code: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        len: [2, 20],
      },
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    department: {
      type: DataTypes.ENUM('sales', 'technical', 'support', 'management', 'accounts', 'hr', 'marketing'),
      allowNull: false,
      defaultValue: 'support',
    },
    level: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
      comment: 'Role hierarchy level (1=highest)',
    },
    responsibilities: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Array of responsibilities',
    },
    permissions: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Array of specific permissions',
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    sort_order: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'staff_roles',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['code'],
        unique: true,
      },
      {
        fields: ['department'],
      },
      {
        fields: ['level'],
      },
      {
        fields: ['is_active'],
      },
      {
        fields: ['sort_order'],
      },
    ],
  });

  // Class methods
  StaffRole.getDefaultRoles = function() {
    return [
      {
        name: 'Sales Manager',
        code: 'SALES_MGR',
        description: 'Manages sales team and activities',
        department: 'sales',
        level: 1,
        responsibilities: [
          'Manage sales team',
          'Set sales targets',
          'Monitor sales performance',
          'Customer relationship management',
        ],
        permissions: ['sales.manage', 'customers.manage', 'reports.view'],
        sort_order: 1,
      },
      {
        name: 'Technical Manager',
        code: 'TECH_MGR',
        description: 'Manages technical team and support',
        department: 'technical',
        level: 1,
        responsibilities: [
          'Manage technical team',
          'Oversee service calls',
          'Technical training',
          'Quality assurance',
        ],
        permissions: ['services.manage', 'technical.manage', 'reports.view'],
        sort_order: 2,
      },
      {
        name: 'Sales Executive',
        code: 'SALES_EXEC',
        description: 'Handles sales activities and customer acquisition',
        department: 'sales',
        level: 2,
        responsibilities: [
          'Generate leads',
          'Customer visits',
          'Product demonstrations',
          'Sales follow-up',
        ],
        permissions: ['sales.create', 'customers.create', 'leads.manage'],
        sort_order: 3,
      },
      {
        name: 'Technical Executive',
        code: 'TECH_EXEC',
        description: 'Provides technical support and services',
        department: 'technical',
        level: 2,
        responsibilities: [
          'Handle service calls',
          'Technical support',
          'Software installation',
          'Customer training',
        ],
        permissions: ['services.create', 'services.update', 'customers.view'],
        sort_order: 4,
      },
      {
        name: 'Support Executive',
        code: 'SUPPORT_EXEC',
        description: 'Provides customer support and assistance',
        department: 'support',
        level: 2,
        responsibilities: [
          'Customer support',
          'Issue resolution',
          'Follow-up calls',
          'Documentation',
        ],
        permissions: ['services.view', 'customers.view', 'support.manage'],
        sort_order: 5,
      },
    ];
  };

  // Associations
  StaffRole.associate = function(models) {
    StaffRole.hasMany(models.Executive, {
      foreignKey: 'staff_role_id',
      as: 'executives',
    });
  };

  return StaffRole;
}
