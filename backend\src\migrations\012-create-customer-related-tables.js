import { DataTypes } from 'sequelize';

export const up = async (queryInterface) => {
  // Create customer_contacts table
  await queryInterface.createTable('customer_contacts', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    customer_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'customers',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    contact_type: {
      type: DataTypes.ENUM('primary', 'secondary', 'technical', 'accounts', 'decision_maker'),
      allowNull: false,
      defaultValue: 'primary',
    },
    title: {
      type: DataTypes.ENUM('Mr', 'Ms', 'Mrs', 'Dr', 'Prof'),
      allowNull: true,
    },
    first_name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    last_name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    designation_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'designations',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    department: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    phone: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    mobile: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    whatsapp: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    extension: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    date_of_birth: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    anniversary_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    address_line_1: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    address_line_2: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    city: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    state: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    country: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: 'India',
    },
    postal_code: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    is_decision_maker: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    is_technical_contact: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    is_billing_contact: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    preferred_communication: {
      type: DataTypes.ENUM('email', 'phone', 'whatsapp', 'sms'),
      allowNull: true,
      defaultValue: 'email',
    },
    communication_preferences: {
      type: DataTypes.JSONB,
      defaultValue: {},
    },
    social_media: {
      type: DataTypes.JSONB,
      defaultValue: {},
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  });

  // Add indexes for customer_contacts
  await queryInterface.addIndex('customer_contacts', ['customer_id']);
  await queryInterface.addIndex('customer_contacts', ['contact_type']);
  await queryInterface.addIndex('customer_contacts', ['email']);
  await queryInterface.addIndex('customer_contacts', ['phone']);
  await queryInterface.addIndex('customer_contacts', ['mobile']);
  await queryInterface.addIndex('customer_contacts', ['is_decision_maker']);
  await queryInterface.addIndex('customer_contacts', ['is_technical_contact']);
  await queryInterface.addIndex('customer_contacts', ['is_billing_contact']);
  await queryInterface.addIndex('customer_contacts', ['is_active']);
};

export const down = async (queryInterface) => {
  await queryInterface.dropTable('customer_contacts');
};
