import { Sequelize } from 'sequelize';
import 'dotenv/config';

const checkDatabaseState = async () => {
  console.log('🔧 Checking database state...');
  
  const sequelize = new Sequelize(
    process.env.DB_NAME,
    process.env.DB_USERNAME,
    process.env.DB_PASSWORD,
    {
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      dialect: 'postgres',
      logging: false,
      dialectOptions: {
        ssl: false
      }
    }
  );

  try {
    await sequelize.authenticate();
    console.log('✅ Connected to database');
    
    // Check if migrations table exists
    const [migrationResults] = await sequelize.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'migrations'
      );
    `);
    
    console.log('📋 Migrations table exists:', migrationResults[0].exists);
    
    if (migrationResults[0].exists) {
      const [migrations] = await sequelize.query('SELECT name FROM migrations ORDER BY id');
      console.log('📝 Executed migrations:', migrations.map(m => m.name));
    }
    
    // List all tables
    const [tables] = await sequelize.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name;
    `);
    
    console.log('📊 Existing tables:', tables.map(t => t.table_name));
    
    // Check if we should reset or continue
    if (tables.length > 1) { // More than just migrations table
      console.log('\n🤔 Database has existing tables. Options:');
      console.log('1. Drop all tables and start fresh');
      console.log('2. Skip existing migrations and continue');
      console.log('3. Manual cleanup required');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await sequelize.close();
  }
};

checkDatabaseState();
