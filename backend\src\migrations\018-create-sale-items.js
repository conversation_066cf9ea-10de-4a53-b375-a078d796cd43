import { DataTypes } from 'sequelize';

export const up = async (queryInterface) => {
  await queryInterface.createTable('sale_items', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    sale_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'sales',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    item_type: {
      type: DataTypes.ENUM('product', 'license', 'service', 'additional_service'),
      allowNull: false,
      defaultValue: 'product',
    },
    product_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'tally_products',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    license_edition_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'license_editions',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    additional_service_id: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'additional_services',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    description: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    hsn_code: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    quantity: {
      type: DataTypes.DECIMAL(10, 3),
      allowNull: false,
      defaultValue: 1.000,
    },
    unit: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'Nos',
    },
    rate: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
    },
    amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
    },
    discount_percentage: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    discount_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    taxable_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
    },
    gst_rate: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: false,
      defaultValue: 18.00,
    },
    cgst_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    sgst_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    igst_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    total_amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.00,
    },
    delivery_status: {
      type: DataTypes.ENUM('pending', 'delivered', 'installed', 'completed'),
      allowNull: false,
      defaultValue: 'pending',
    },
    delivery_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    installation_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    warranty_period: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    warranty_start_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    warranty_end_date: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    serial_numbers: {
      type: DataTypes.JSONB,
      defaultValue: [],
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  });

  // Add indexes
  await queryInterface.addIndex('sale_items', ['sale_id']);
  await queryInterface.addIndex('sale_items', ['item_type']);
  await queryInterface.addIndex('sale_items', ['product_id']);
  await queryInterface.addIndex('sale_items', ['license_edition_id']);
  await queryInterface.addIndex('sale_items', ['additional_service_id']);
  await queryInterface.addIndex('sale_items', ['delivery_status']);
};

export const down = async (queryInterface) => {
  await queryInterface.dropTable('sale_items');
};
