import { DataTypes } from 'sequelize';

export default function(sequelize) {
  const LicenseEdition = sequelize.define('LicenseEdition', {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        len: [2, 100],
      },
    },
    code: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        len: [2, 20],
      },
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    version: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    price: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    annual_maintenance_charge: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00,
    },
    features: {
      type: DataTypes.JSONB,
      defaultValue: [],
      comment: 'Array of features included in this edition',
    },
    max_companies: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Maximum number of companies allowed',
    },
    max_users: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Maximum number of users allowed',
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    sort_order: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  }, {
    tableName: 'license_editions',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['code'],
        unique: true,
      },
      {
        fields: ['is_active'],
      },
      {
        fields: ['sort_order'],
      },
    ],
  });

  // Class methods
  LicenseEdition.getDefaultEditions = function() {
    return [
      {
        name: 'Tally.ERP 9 Silver',
        code: 'TERP9_SILVER',
        description: 'Basic accounting and inventory management',
        version: '6.6.3',
        price: 18000.00,
        annual_maintenance_charge: 5400.00,
        features: ['Accounting', 'Inventory', 'Statutory Reports'],
        max_companies: 1,
        max_users: 1,
        sort_order: 1,
      },
      {
        name: 'Tally.ERP 9 Gold',
        code: 'TERP9_GOLD',
        description: 'Multi-user accounting and inventory management',
        version: '6.6.3',
        price: 54000.00,
        annual_maintenance_charge: 16200.00,
        features: ['Accounting', 'Inventory', 'Statutory Reports', 'Multi-User'],
        max_companies: 999,
        max_users: 999,
        sort_order: 2,
      },
      {
        name: 'TallyPrime Silver',
        code: 'TPRIME_SILVER',
        description: 'Modern business management software',
        version: '4.0',
        price: 18000.00,
        annual_maintenance_charge: 5400.00,
        features: ['Accounting', 'Inventory', 'GST', 'Banking'],
        max_companies: 1,
        max_users: 1,
        sort_order: 3,
      },
      {
        name: 'TallyPrime Gold',
        code: 'TPRIME_GOLD',
        description: 'Multi-user business management software',
        version: '4.0',
        price: 54000.00,
        annual_maintenance_charge: 16200.00,
        features: ['Accounting', 'Inventory', 'GST', 'Banking', 'Multi-User'],
        max_companies: 999,
        max_users: 999,
        sort_order: 4,
      },
    ];
  };

  // Instance methods
  LicenseEdition.prototype.getAMCPercentage = function() {
    if (this.price > 0) {
      return (this.annual_maintenance_charge / this.price) * 100;
    }
    return 0;
  };

  // Associations
  LicenseEdition.associate = function(models) {
    LicenseEdition.hasMany(models.CustomerTSS, {
      foreignKey: 'license_edition_id',
      as: 'customerTSS',
    });

    LicenseEdition.hasMany(models.SaleItem, {
      foreignKey: 'license_edition_id',
      as: 'saleItems',
    });
  };

  return LicenseEdition;
}
