import { Sequelize } from 'sequelize';
import bcrypt from 'bcryptjs';
import 'dotenv/config';

const createAdminUser = async () => {
  console.log('👤 Creating admin user...');

  const sequelize = new Sequelize(
    process.env.DB_NAME,
    process.env.DB_USERNAME,
    process.env.DB_PASSWORD,
    {
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      dialect: 'postgres',
      logging: false,
      dialectOptions: {
        ssl: false
      }
    }
  );

  try {
    await sequelize.authenticate();
    console.log('✅ Connected to database');

    // Check if admin user already exists
    const [existingUsers] = await sequelize.query(`
      SELECT email FROM users WHERE email = '<EMAIL>'
    `);

    if (existingUsers.length > 0) {
      console.log('ℹ️  Admin user already exists!');
      console.log('📧 Email: <EMAIL>');
      console.log('🔑 Password: admin123');
      return;
    }

    // Get or create default tenant
    let [tenants] = await sequelize.query(`
      SELECT id FROM tenants LIMIT 1
    `);

    let tenantId;
    if (tenants.length === 0) {
      console.log('📝 Creating default tenant...');
      const [newTenant] = await sequelize.query(`
        INSERT INTO tenants (id, name, slug, domain, is_active, created_at, updated_at)
        VALUES (gen_random_uuid(), 'Default Tenant', 'default', 'localhost', true, NOW(), NOW())
        RETURNING id
      `);
      tenantId = newTenant[0].id;
    } else {
      tenantId = tenants[0].id;
    }

    // Get admin role ID
    const [adminRoles] = await sequelize.query(`
      SELECT id FROM roles WHERE slug = 'admin' LIMIT 1
    `);

    if (adminRoles.length === 0) {
      throw new Error('Admin role not found. Please run seeders first.');
    }

    const adminRoleId = adminRoles[0].id;

    // Hash password
    const hashedPassword = await bcrypt.hash('admin123', 12);

    // Create admin user
    const [result] = await sequelize.query(`
      INSERT INTO users (
        id,
        tenant_id,
        first_name,
        last_name,
        email,
        password,
        is_verified,
        is_active,
        created_at,
        updated_at
      ) VALUES (
        gen_random_uuid(),
        :tenantId,
        'Admin',
        'User',
        '<EMAIL>',
        :password,
        true,
        true,
        NOW(),
        NOW()
      ) RETURNING id
    `, {
      replacements: { tenantId, password: hashedPassword }
    });

    const userId = result[0].id;

    // Assign admin role to user
    await sequelize.query(`
      INSERT INTO user_roles (id, user_id, role_id, created_at, updated_at)
      VALUES (gen_random_uuid(), :userId, :roleId, NOW(), NOW())
    `, {
      replacements: { userId, roleId: adminRoleId }
    });

    console.log('✅ Admin user created successfully!');
    console.log('');
    console.log('🎯 LOGIN CREDENTIALS:');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: admin123');
    console.log('');
    console.log('🚀 You can now login to the application!');

  } catch (error) {
    console.error('❌ Error creating admin user:', error.message);
  } finally {
    await sequelize.close();
  }
};

createAdminUser();
