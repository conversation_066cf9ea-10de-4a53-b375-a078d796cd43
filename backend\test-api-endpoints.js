import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3001/api/v1';

// Test credentials
const credentials = {
  email: '<EMAIL>',
  password: 'admin123'
};

let authToken = null;

// Helper function to make authenticated requests
const makeRequest = async (endpoint, options = {}) => {
  const url = `${BASE_URL}${endpoint}`;
  const headers = {
    'Content-Type': 'application/json',
    ...options.headers
  };

  if (authToken) {
    headers.Authorization = `Bearer ${authToken}`;
  }

  const response = await fetch(url, {
    ...options,
    headers
  });

  const data = await response.json();
  return { response, data };
};

// Test login
const testLogin = async () => {
  console.log('\n🔐 Testing Login...');
  try {
    const { response, data } = await makeRequest('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials)
    });

    if (response.ok) {
      authToken = data.data?.token || data.token;
      console.log('✅ Login successful');
      console.log('🎫 Token received:', authToken ? 'Yes' : 'No');
      return true;
    } else {
      console.log('❌ Login failed:', data.message);
      return false;
    }
  } catch (error) {
    console.log('❌ Login error:', error.message);
    return false;
  }
};

// Test dashboard overview
const testDashboard = async () => {
  console.log('\n📊 Testing Dashboard Overview...');
  try {
    const { response, data } = await makeRequest('/dashboard/overview');
    
    if (response.ok) {
      console.log('✅ Dashboard overview successful');
      console.log('📈 Data received:', Object.keys(data.data || {}));
      return true;
    } else {
      console.log('❌ Dashboard overview failed:', response.status, data.message);
      return false;
    }
  } catch (error) {
    console.log('❌ Dashboard overview error:', error.message);
    return false;
  }
};

// Test customers endpoint
const testCustomers = async () => {
  console.log('\n👥 Testing Customers...');
  try {
    const { response, data } = await makeRequest('/customers?limit=5');
    
    if (response.ok) {
      console.log('✅ Customers endpoint successful');
      console.log('👥 Customers count:', data.data?.customers?.length || 0);
      return true;
    } else {
      console.log('❌ Customers endpoint failed:', response.status, data.message);
      return false;
    }
  } catch (error) {
    console.log('❌ Customers endpoint error:', error.message);
    return false;
  }
};

// Test service calls endpoint
const testServiceCalls = async () => {
  console.log('\n📞 Testing Service Calls...');
  try {
    const { response, data } = await makeRequest('/service-calls?limit=5');
    
    if (response.ok) {
      console.log('✅ Service calls endpoint successful');
      console.log('📞 Service calls count:', data.data?.serviceCalls?.length || 0);
      return true;
    } else {
      console.log('❌ Service calls endpoint failed:', response.status, data.message);
      return false;
    }
  } catch (error) {
    console.log('❌ Service calls endpoint error:', error.message);
    return false;
  }
};

// Run all tests
const runTests = async () => {
  console.log('🧪 Starting API Tests...');
  console.log('🎯 Target:', BASE_URL);
  
  const loginSuccess = await testLogin();
  if (!loginSuccess) {
    console.log('\n❌ Cannot proceed without authentication');
    return;
  }

  const results = {
    dashboard: await testDashboard(),
    customers: await testCustomers(),
    serviceCalls: await testServiceCalls()
  };

  console.log('\n📋 Test Results Summary:');
  console.log('🔐 Login:', loginSuccess ? '✅' : '❌');
  console.log('📊 Dashboard:', results.dashboard ? '✅' : '❌');
  console.log('👥 Customers:', results.customers ? '✅' : '❌');
  console.log('📞 Service Calls:', results.serviceCalls ? '✅' : '❌');

  const allPassed = loginSuccess && Object.values(results).every(r => r);
  console.log('\n🎯 Overall Result:', allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED');
};

// Handle module import
if (import.meta.url === `file://${process.argv[1]}`) {
  runTests().catch(console.error);
}

export { runTests };
